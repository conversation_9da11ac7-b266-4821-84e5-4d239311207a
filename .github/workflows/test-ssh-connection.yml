name: SSH Remote Command

on:
  workflow_dispatch:
    inputs:
      server_hostname:
        description: 'Server hostname or IP address'
        required: true
        default: '*************'
      username:
        description: 'SSH username'
        required: true
        default: 'devops_dba'

jobs:
  ssh-job:
    runs-on: [Self-Hosted, Linux, MTP]
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Run remote SSH command as another user
      env:
        SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY_ }}
        SSH_PASSWORD: ${{ secrets.SSH_PASSWORD_ }}

      run: |
        echo "$SSH_PRIVATE_KEY" > key.pem
        chmod 600 key.pem

        # Create a wrapper script for ssh using sshpass
        sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no -i key.pem ${{ inputs.username }}@${{ inputs.server_hostname }} << 'EOF'
          echo "Switching to applpchebs"
          sudo -su applpchebs
          whoami
          ls -la /EBS_share/EBSPCH/SOX_AUDIT/
        EOF
