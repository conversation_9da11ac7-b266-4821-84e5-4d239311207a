#!/usr/bin/env python3
"""
<PERSON>ript to extract all repository information from the existing repos.json file
and save them to CSV files with comprehensive details.
"""

import json
import csv
import os
from datetime import datetime

def extract_all_repos_from_json():
    """Extract all repository information from repos.json and save to CSV files."""
    
    # Input and output file paths
    json_file = '.github/workflows/repos.json'
    detailed_csv = 'all_repositories_detailed.csv'
    names_csv = 'all_repository_names.csv'
    
    try:
        # Read the JSON file
        print(f"Reading repository data from {json_file}...")
        with open(json_file, 'r', encoding='utf-8') as f:
            repos_data = json.load(f)
        
        print(f"Found {len(repos_data)} repositories in the JSON file")
        
        # Define fields for detailed CSV
        detailed_fields = [
            'name',
            'full_name',
            'description',
            'private',
            'language',
            'created_at',
            'updated_at',
            'pushed_at',
            'size',
            'stargazers_count',
            'watchers_count',
            'forks_count',
            'open_issues_count',
            'default_branch',
            'archived',
            'disabled',
            'html_url',
            'clone_url',
            'ssh_url',
            'visibility'
        ]
        
        # Create detailed CSV file
        print(f"Creating detailed CSV file: {detailed_csv}")
        with open(detailed_csv, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=detailed_fields)
            writer.writeheader()
            
            for repo in repos_data:
                # Extract only the fields we want, handling missing fields gracefully
                row = {}
                for field in detailed_fields:
                    value = repo.get(field, '')
                    # Clean up date fields to be more readable
                    if field.endswith('_at') and value:
                        try:
                            # Convert ISO format to more readable format
                            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                            value = dt.strftime('%Y-%m-%d %H:%M:%S UTC')
                        except:
                            pass  # Keep original value if parsing fails
                    row[field] = value
                writer.writerow(row)
        
        # Create simple names CSV file
        print(f"Creating repository names CSV file: {names_csv}")
        with open(names_csv, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Repository Name'])
            for repo in repos_data:
                writer.writerow([repo.get('name', '')])
        
        # Print summary statistics
        print(f"\nSummary:")
        print(f"Total repositories: {len(repos_data)}")
        
        # Count by language
        languages = {}
        private_count = 0
        archived_count = 0
        
        for repo in repos_data:
            # Language statistics
            lang = repo.get('language') or 'Unknown'
            languages[lang] = languages.get(lang, 0) + 1
            
            # Privacy statistics
            if repo.get('private', False):
                private_count += 1
                
            # Archive statistics
            if repo.get('archived', False):
                archived_count += 1
        
        print(f"Private repositories: {private_count}")
        print(f"Archived repositories: {archived_count}")
        print(f"Public repositories: {len(repos_data) - private_count}")
        
        print(f"\nTop 10 languages:")
        sorted_languages = sorted(languages.items(), key=lambda x: x[1], reverse=True)
        for i, (lang, count) in enumerate(sorted_languages[:10]):
            print(f"  {i+1:2d}. {lang}: {count}")
        
        # Show first few repository names
        print(f"\nFirst 10 repositories:")
        for i, repo in enumerate(repos_data[:10]):
            name = repo.get('name', 'Unknown')
            lang = repo.get('language', 'Unknown')
            print(f"  {i+1:2d}. {name} ({lang})")
        
        if len(repos_data) > 10:
            print(f"  ... and {len(repos_data) - 10} more")
        
        print(f"\nFiles created:")
        print(f"  - {detailed_csv} (detailed information)")
        print(f"  - {names_csv} (repository names only)")
        
    except FileNotFoundError:
        print(f"Error: Could not find the file {json_file}")
        print("Make sure the repos.json file exists in the .github/workflows/ directory")
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format in {json_file}: {e}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    extract_all_repos_from_json()
