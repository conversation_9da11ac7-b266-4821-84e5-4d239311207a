name,full_name,description,private,language,created_at,updated_at,pushed_at,size,stargazers_count,watchers_count,forks_count,open_issues_count,default_branch,archived,disabled,html_url,clone_url
34by34website,cox-cei/34by34website,This is the website that hosts information about the Cox program 34 by 34 called Journey to 34 by 34 located at https:=//34by34.coxenterprises.com/,True,,2023-06-02T12:19:44Z,2024-07-24T02:09:58Z,2023-06-02T12:19:44Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/34by34website,https://github.com/cox-cei/34by34website.git
accelerated-engineering-onboarding,cox-cei/accelerated-engineering-onboarding,This is a respository to onboard users to using Accelerated Engineering. ,True,,2025-05-28T20:31:10Z,2025-07-29T19:26:18Z,2025-07-29T19:26:14Z,67,1,1,0,0,main,False,False,https://github.com/cox-cei/accelerated-engineering-onboarding,https://github.com/cox-cei/accelerated-engineering-onboarding.git
AI-FileQueryLoop-Function,cox-cei/AI-FileQueryLoop-Function,,True,C#,2025-01-23T16:32:22Z,2025-03-18T17:57:50Z,2025-03-18T17:57:48Z,185,0,0,0,0,master,False,False,https://github.com/cox-cei/AI-FileQueryLoop-Function,https://github.com/cox-cei/AI-FileQueryLoop-Function.git
AIAssistant-FileQueryLoop-API,cox-cei/AIAssistant-FileQueryLoop-API,,True,C#,2024-12-04T18:46:51Z,2025-01-22T13:47:07Z,2025-01-17T15:35:13Z,110,1,1,0,0,master,False,False,https://github.com/cox-cei/AIAssistant-FileQueryLoop-API,https://github.com/cox-cei/AIAssistant-FileQueryLoop-API.git
AIFactory-core-backend,cox-cei/AIFactory-core-backend,.NET Web API utilizing Azure Open AI Assistant,True,C#,2024-11-12T21:16:25Z,2024-12-13T16:32:40Z,2024-12-13T16:32:36Z,76,0,0,0,0,master,False,False,https://github.com/cox-cei/AIFactory-core-backend,https://github.com/cox-cei/AIFactory-core-backend.git
aifactory-core-frontend,cox-cei/aifactory-core-frontend,React Web App - frontend for AIFactory Core Capabilities.,True,TypeScript,2024-11-12T21:18:34Z,2024-12-16T18:10:34Z,2024-12-16T18:10:31Z,4145,0,0,0,0,main,False,False,https://github.com/cox-cei/aifactory-core-frontend,https://github.com/cox-cei/aifactory-core-frontend.git
aifactory-llmops-promptflow,cox-cei/aifactory-llmops-promptflow,None,True,Python,2024-06-25T19:40:04Z,2024-11-12T19:57:47Z,2024-11-12T19:57:43Z,566,0,0,0,1,main,False,False,https://github.com/cox-cei/aifactory-llmops-promptflow,https://github.com/cox-cei/aifactory-llmops-promptflow.git
ansible-builder,cox-cei/ansible-builder,for building execution environments,True,,2025-05-28T19:35:30Z,2025-05-28T19:35:32Z,2025-05-28T19:35:30Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-builder,https://github.com/cox-cei/ansible-builder.git
ansible-executionEnvironments,cox-cei/ansible-executionEnvironments,None,True,,2024-05-30T17:24:44Z,2024-10-18T22:45:28Z,2024-05-30T17:24:44Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-executionEnvironments,https://github.com/cox-cei/ansible-executionEnvironments.git
ansible-oci-bi-patch-automation,cox-cei/ansible-oci-bi-patch-automation,,True,,2025-04-28T14:47:00Z,2025-07-10T14:02:40Z,2025-07-22T20:03:58Z,77,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-oci-bi-patch-automation,https://github.com/cox-cei/ansible-oci-bi-patch-automation.git
ansible-oci-linuxkernel,cox-cei/ansible-oci-linuxkernel,,True,,2025-05-27T16:27:37Z,2025-07-18T18:21:36Z,2025-07-21T16:27:19Z,21,1,1,0,0,dev,False,False,https://github.com/cox-cei/ansible-oci-linuxkernel,https://github.com/cox-cei/ansible-oci-linuxkernel.git
ansible-oci-uptime,cox-cei/ansible-oci-uptime,,True,,2025-04-29T13:21:14Z,2025-06-10T15:52:36Z,2025-07-15T19:23:24Z,42,1,1,0,0,dev,False,False,https://github.com/cox-cei/ansible-oci-uptime,https://github.com/cox-cei/ansible-oci-uptime.git
ansible-role-addDisk,cox-cei/ansible-role-addDisk,None,True,,2023-11-08T16:17:35Z,2024-10-18T19:27:20Z,2024-10-09T14:49:44Z,10,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-addDisk,https://github.com/cox-cei/ansible-role-addDisk.git
ansible-role-addVirtIp,cox-cei/ansible-role-addVirtIp,None,True,Jinja,2024-05-16T14:37:43Z,2024-10-18T22:45:59Z,2024-05-21T17:53:25Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-addVirtIp,https://github.com/cox-cei/ansible-role-addVirtIp.git
ansible-role-adiTest,cox-cei/ansible-role-adiTest,None,True,,2023-12-12T14:20:33Z,2025-01-24T20:13:46Z,2025-01-24T20:13:42Z,77,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-adiTest,https://github.com/cox-cei/ansible-role-adiTest.git
ansible-role-adobeAcrobat,cox-cei/ansible-role-adobeAcrobat,None,True,,2023-11-08T16:15:39Z,2024-10-18T19:28:00Z,2023-12-01T20:43:02Z,7,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-adobeAcrobat,https://github.com/cox-cei/ansible-role-adobeAcrobat.git
ansible-role-agentUsers,cox-cei/ansible-role-agentUsers,None,True,,2023-11-08T16:18:24Z,2024-10-18T19:28:58Z,2024-05-24T12:40:10Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-agentUsers,https://github.com/cox-cei/ansible-role-agentUsers.git
ansible-role-ansibleSetup,cox-cei/ansible-role-ansibleSetup,None,True,,2023-11-08T16:15:46Z,2025-01-24T20:27:35Z,2025-01-24T20:28:44Z,14935,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-ansibleSetup,https://github.com/cox-cei/ansible-role-ansibleSetup.git
ansible-role-asserter,cox-cei/ansible-role-asserter,None,True,Jinja,2024-03-07T17:15:44Z,2024-10-18T19:29:39Z,2024-08-16T14:22:09Z,26,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-asserter,https://github.com/cox-cei/ansible-role-asserter.git
ansible-role-biApp,cox-cei/ansible-role-biApp,None,True,,2023-11-08T16:13:56Z,2025-06-16T14:10:40Z,2025-06-16T14:10:36Z,55,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-biApp,https://github.com/cox-cei/ansible-role-biApp.git
ansible-role-bigFix,cox-cei/ansible-role-bigFix,None,True,,2023-11-08T16:18:38Z,2024-10-18T19:30:26Z,2024-07-30T14:50:57Z,25034,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-bigFix,https://github.com/cox-cei/ansible-role-bigFix.git
ansible-role-bluePrism,cox-cei/ansible-role-bluePrism,None,True,,2023-11-08T16:13:17Z,2024-10-18T19:30:29Z,2023-12-01T21:07:50Z,43744,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-bluePrism,https://github.com/cox-cei/ansible-role-bluePrism.git
ansible-role-ceiCaCert,cox-cei/ansible-role-ceiCaCert,None,True,,2023-11-08T16:14:56Z,2024-10-18T19:31:03Z,2023-12-01T20:53:58Z,5,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-ceiCaCert,https://github.com/cox-cei/ansible-role-ceiCaCert.git
ansible-role-celonisExtractorAgent,cox-cei/ansible-role-celonisExtractorAgent,None,True,,2023-11-20T20:17:34Z,2024-10-18T19:31:08Z,2023-11-28T16:10:38Z,13,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-celonisExtractorAgent,https://github.com/cox-cei/ansible-role-celonisExtractorAgent.git
ansible-role-chrome,cox-cei/ansible-role-chrome,None,True,,2023-11-08T16:13:07Z,2024-10-18T19:31:38Z,2024-09-20T19:58:46Z,49100,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-chrome,https://github.com/cox-cei/ansible-role-chrome.git
ansible-role-chrony,cox-cei/ansible-role-chrony,None,True,,2023-11-08T16:17:32Z,2024-10-18T19:31:51Z,2023-11-28T17:27:38Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-chrony,https://github.com/cox-cei/ansible-role-chrony.git
ansible-role-cmTrace,cox-cei/ansible-role-cmTrace,None,True,,2023-11-08T16:13:03Z,2024-10-18T19:32:15Z,2023-12-01T21:04:05Z,284,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-cmTrace,https://github.com/cox-cei/ansible-role-cmTrace.git
ansible-role-cohesityAgent,cox-cei/ansible-role-cohesityAgent,None,True,,2023-11-08T16:18:29Z,2024-10-18T19:32:34Z,2024-05-21T13:48:19Z,91045,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-cohesityAgent,https://github.com/cox-cei/ansible-role-cohesityAgent.git
ansible-role-configureIPA,cox-cei/ansible-role-configureIPA,None,True,Python,2023-11-08T16:17:37Z,2025-07-09T19:17:20Z,2025-07-09T19:18:03Z,125,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-configureIPA,https://github.com/cox-cei/ansible-role-configureIPA.git
ansible-role-conjurPrep,cox-cei/ansible-role-conjurPrep,None,True,,2024-08-08T17:50:54Z,2024-10-18T22:46:06Z,2024-08-08T18:22:12Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-conjurPrep,https://github.com/cox-cei/ansible-role-conjurPrep.git
ansible-role-containerSleep,cox-cei/ansible-role-containerSleep,None,True,,2023-11-08T16:14:47Z,2024-10-18T19:33:17Z,2023-12-01T21:03:11Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-containerSleep,https://github.com/cox-cei/ansible-role-containerSleep.git
ansible-role-controlMAgents,cox-cei/ansible-role-controlMAgents,None,True,,2024-06-14T12:54:49Z,2024-10-18T22:46:39Z,2024-07-11T19:58:57Z,10,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-controlMAgents,https://github.com/cox-cei/ansible-role-controlMAgents.git
ansible-role-createAnsibleInfoFile,cox-cei/ansible-role-createAnsibleInfoFile,None,True,,2023-11-08T16:14:10Z,2025-07-10T13:09:54Z,2025-07-10T13:09:50Z,12,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-createAnsibleInfoFile,https://github.com/cox-cei/ansible-role-createAnsibleInfoFile.git
ansible-role-delphix,cox-cei/ansible-role-delphix,None,True,,2023-11-08T16:14:59Z,2024-10-18T19:33:58Z,2023-12-01T20:54:29Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-delphix,https://github.com/cox-cei/ansible-role-delphix.git
ansible-role-dnsRZSec,cox-cei/ansible-role-dnsRZSec,None,True,Jinja,2024-06-10T14:10:25Z,2025-03-14T16:25:54Z,2025-03-14T16:25:50Z,26,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-dnsRZSec,https://github.com/cox-cei/ansible-role-dnsRZSec.git
ansible-role-dnsUpdates,cox-cei/ansible-role-dnsUpdates,None,True,,2023-11-08T16:15:55Z,2024-10-18T19:34:11Z,2023-12-01T20:40:56Z,10,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-dnsUpdates,https://github.com/cox-cei/ansible-role-dnsUpdates.git
ansible-role-dotnet,cox-cei/ansible-role-dotnet,None,True,,2023-11-08T16:14:50Z,2024-10-18T19:34:31Z,2025-01-08T19:58:35Z,148933,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-dotnet,https://github.com/cox-cei/ansible-role-dotnet.git
ansible-role-ecc,cox-cei/ansible-role-ecc,for building ecc servers,True,Jinja,2024-12-04T16:40:18Z,2024-12-04T18:43:25Z,2024-12-04T18:43:21Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-ecc,https://github.com/cox-cei/ansible-role-ecc.git
ansible-role-exadata,cox-cei/ansible-role-exadata,None,True,,2024-03-21T18:49:05Z,2024-10-18T19:34:47Z,2024-07-11T12:26:08Z,21,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-exadata,https://github.com/cox-cei/ansible-role-exadata.git
ansible-role-flushHandlers,cox-cei/ansible-role-flushHandlers,None,True,,2023-11-08T16:16:51Z,2024-10-18T19:35:06Z,2023-12-01T20:35:01Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-flushHandlers,https://github.com/cox-cei/ansible-role-flushHandlers.git
ansible-role-growFs,cox-cei/ansible-role-growFs,None,True,,2023-11-08T16:17:28Z,2024-10-18T19:35:26Z,2023-11-28T17:29:06Z,5,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-growFs,https://github.com/cox-cei/ansible-role-growFs.git
ansible-role-growSwap,cox-cei/ansible-role-growSwap,None,True,,2023-11-08T16:13:05Z,2024-10-18T19:35:40Z,2023-12-01T21:04:34Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-growSwap,https://github.com/cox-cei/ansible-role-growSwap.git
ansible-role-iis,cox-cei/ansible-role-iis,None,True,,2023-11-08T16:19:10Z,2024-10-18T19:36:03Z,2023-11-28T16:13:37Z,7,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-iis,https://github.com/cox-cei/ansible-role-iis.git
ansible-role-java,cox-cei/ansible-role-java,None,True,,2023-11-08T16:14:54Z,2024-10-18T19:36:15Z,2023-12-01T20:48:27Z,60898,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-java,https://github.com/cox-cei/ansible-role-java.git
ansible-role-javaUpdate,cox-cei/ansible-role-javaUpdate,None,True,,2023-11-08T16:15:48Z,2024-10-18T19:36:39Z,2023-12-01T20:44:56Z,70,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-javaUpdate,https://github.com/cox-cei/ansible-role-javaUpdate.git
ansible-role-kdump,cox-cei/ansible-role-kdump,,True,,2025-01-23T20:50:56Z,2025-01-23T20:50:58Z,2025-01-23T20:50:56Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-kdump,https://github.com/cox-cei/ansible-role-kdump.git
ansible-role-kronos,cox-cei/ansible-role-kronos,None,True,,2023-11-08T16:16:38Z,2024-10-18T19:36:54Z,2023-12-01T20:37:59Z,12,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-kronos,https://github.com/cox-cei/ansible-role-kronos.git
ansible-role-kspliceInst,cox-cei/ansible-role-kspliceInst,None,True,,2023-11-08T16:16:53Z,2024-10-18T19:37:27Z,2024-09-11T19:10:42Z,5,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-kspliceInst,https://github.com/cox-cei/ansible-role-kspliceInst.git
ansible-role-linuxAdmins,cox-cei/ansible-role-linuxAdmins,None,True,,2023-11-08T16:18:22Z,2024-10-18T19:37:36Z,2024-04-19T14:23:17Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-linuxAdmins,https://github.com/cox-cei/ansible-role-linuxAdmins.git
ansible-role-linuxInitialConfigFiles,cox-cei/ansible-role-linuxInitialConfigFiles,None,True,Shell,2023-11-08T16:18:20Z,2024-10-18T19:38:11Z,2024-07-26T17:09:25Z,11,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-linuxInitialConfigFiles,https://github.com/cox-cei/ansible-role-linuxInitialConfigFiles.git
ansible-role-linuxNFSMounts,cox-cei/ansible-role-linuxNFSMounts,None,True,,2024-04-25T18:45:12Z,2024-10-18T22:47:23Z,2024-04-26T18:15:32Z,7,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-linuxNFSMounts,https://github.com/cox-cei/ansible-role-linuxNFSMounts.git
ansible-role-linuxP1Accounts,cox-cei/ansible-role-linuxP1Accounts,None,True,,2023-11-08T16:17:26Z,2024-10-18T19:38:18Z,2024-04-19T17:53:56Z,42,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-linuxP1Accounts,https://github.com/cox-cei/ansible-role-linuxP1Accounts.git
ansible-role-logRotate,cox-cei/ansible-role-logRotate,None,True,Jinja,2023-11-08T16:16:34Z,2024-10-18T19:38:44Z,2023-12-01T20:39:09Z,982,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-logRotate,https://github.com/cox-cei/ansible-role-logRotate.git
ansible-role-mlocate,cox-cei/ansible-role-mlocate,None,True,,2023-12-12T14:20:45Z,2024-10-18T19:38:58Z,2023-12-12T20:21:36Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-mlocate,https://github.com/cox-cei/ansible-role-mlocate.git
ansible-role-mscplusplus,cox-cei/ansible-role-mscplusplus,None,True,,2023-11-08T16:16:42Z,2024-10-18T19:39:21Z,2023-12-01T20:36:52Z,9299,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-mscplusplus,https://github.com/cox-cei/ansible-role-mscplusplus.git
ansible-role-msdefender-remediation,cox-cei/ansible-role-msdefender-remediation,this is to upgrade older windows servers to newer version of Defender,True,,2025-05-15T12:07:40Z,2025-05-28T14:55:08Z,2025-05-28T14:55:03Z,8,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-msdefender-remediation,https://github.com/cox-cei/ansible-role-msdefender-remediation.git
ansible-role-msDefender2,cox-cei/ansible-role-msDefender2,None,True,Jinja,2023-11-08T16:19:12Z,2024-10-18T19:39:35Z,2024-08-22T18:47:44Z,15,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-msDefender2,https://github.com/cox-cei/ansible-role-msDefender2.git
ansible-role-mssql,cox-cei/ansible-role-mssql,None,True,,2023-11-08T16:16:49Z,2025-06-20T19:52:25Z,2025-06-23T17:32:29Z,50,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-mssql,https://github.com/cox-cei/ansible-role-mssql.git
ansible-role-mssqlclient,cox-cei/ansible-role-mssqlclient,None,True,,2023-11-08T16:17:30Z,2024-10-18T19:40:17Z,2023-11-28T17:28:32Z,11,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-mssqlclient,https://github.com/cox-cei/ansible-role-mssqlclient.git
ansible-role-netapp,cox-cei/ansible-role-netapp,None,True,,2023-11-08T16:15:44Z,2024-10-18T19:40:29Z,2023-12-01T20:43:55Z,17,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-netapp,https://github.com/cox-cei/ansible-role-netapp.git
ansible-role-netmon,cox-cei/ansible-role-netmon,None,True,,2023-11-08T16:15:50Z,2024-10-18T19:40:53Z,2023-12-01T20:45:30Z,6615,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-netmon,https://github.com/cox-cei/ansible-role-netmon.git
ansible-role-newRelic,cox-cei/ansible-role-newRelic,None,True,Jinja,2023-11-08T16:14:15Z,2025-03-20T16:17:13Z,2025-03-20T16:17:11Z,105,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-newRelic,https://github.com/cox-cei/ansible-role-newRelic.git
ansible-role-newRelicGalaxy090,cox-cei/ansible-role-newRelicGalaxy090,None,True,Jinja,2024-02-02T17:49:50Z,2024-10-18T19:41:27Z,2024-02-02T17:58:48Z,18,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-newRelicGalaxy090,https://github.com/cox-cei/ansible-role-newRelicGalaxy090.git
ansible-role-ociCliPrep,cox-cei/ansible-role-ociCliPrep,None,True,,2024-08-14T14:11:00Z,2024-10-19T00:31:12Z,2024-08-14T14:43:44Z,1,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-ociCliPrep,https://github.com/cox-cei/ansible-role-ociCliPrep.git
ansible-role-ociEBS,cox-cei/ansible-role-ociEBS,None,True,Jinja,2023-11-08T16:17:24Z,2024-10-18T19:41:53Z,2024-09-06T14:12:12Z,83,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-ociEBS,https://github.com/cox-cei/ansible-role-ociEBS.git
ansible-role-ociKspliceRepoConfig,cox-cei/ansible-role-ociKspliceRepoConfig,None,True,Shell,2024-02-09T15:55:52Z,2025-07-08T11:54:41Z,2025-07-08T11:54:37Z,82,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-ociKspliceRepoConfig,https://github.com/cox-cei/ansible-role-ociKspliceRepoConfig.git
ansible-role-ociprep,cox-cei/ansible-role-ociprep,None,True,,2024-03-21T18:49:07Z,2024-10-18T19:42:33Z,2024-03-22T13:01:06Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-ociprep,https://github.com/cox-cei/ansible-role-ociprep.git
ansible-role-ociVmDb,cox-cei/ansible-role-ociVmDb,None,True,,2024-03-22T18:18:54Z,2024-10-18T19:42:45Z,2024-07-01T17:53:37Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-ociVmDb,https://github.com/cox-cei/ansible-role-ociVmDb.git
ansible-role-ociVmDbWebCenter,cox-cei/ansible-role-ociVmDbWebCenter,None,True,,2024-04-24T15:22:53Z,2024-10-18T22:47:32Z,2024-07-11T19:55:59Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-ociVmDbWebCenter,https://github.com/cox-cei/ansible-role-ociVmDbWebCenter.git
ansible-role-office,cox-cei/ansible-role-office,None,True,,2023-11-08T16:16:40Z,2025-03-18T15:46:41Z,2025-03-20T21:35:50Z,6788,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-office,https://github.com/cox-cei/ansible-role-office.git
ansible-role-oicAgent,cox-cei/ansible-role-oicAgent,None,True,,2023-11-08T16:13:58Z,2024-12-02T18:43:51Z,2024-12-02T18:43:47Z,13,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-oicAgent,https://github.com/cox-cei/ansible-role-oicAgent.git
ansible-role-oneStream,cox-cei/ansible-role-oneStream,None,True,,2024-05-14T15:31:45Z,2024-10-18T22:48:04Z,2024-05-22T18:57:39Z,23,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-oneStream,https://github.com/cox-cei/ansible-role-oneStream.git
ansible-role-oracle,cox-cei/ansible-role-oracle,None,True,Inno Setup,2023-12-12T14:20:48Z,2024-10-18T19:43:55Z,2023-12-12T20:22:07Z,13,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-oracle,https://github.com/cox-cei/ansible-role-oracle.git
ansible-role-oracleAdmin,cox-cei/ansible-role-oracleAdmin,None,True,,2023-12-12T14:20:43Z,2024-10-18T19:43:53Z,2023-12-12T20:21:03Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-oracleAdmin,https://github.com/cox-cei/ansible-role-oracleAdmin.git
ansible-role-oracleGrc,cox-cei/ansible-role-oracleGrc,None,True,Jinja,2023-12-12T14:20:36Z,2024-10-18T19:44:29Z,2023-12-12T20:06:36Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-oracleGrc,https://github.com/cox-cei/ansible-role-oracleGrc.git
ansible-role-patchRHEL,cox-cei/ansible-role-patchRHEL,None,True,,2023-11-08T16:13:53Z,2024-10-18T19:44:32Z,2023-12-01T21:09:17Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-patchRHEL,https://github.com/cox-cei/ansible-role-patchRHEL.git
ansible-role-patchRHELReboot,cox-cei/ansible-role-patchRHELReboot,None,True,,2023-11-08T16:15:53Z,2024-10-18T19:45:06Z,2023-12-01T20:46:03Z,4,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-patchRHELReboot,https://github.com/cox-cei/ansible-role-patchRHELReboot.git
ansible-role-proWatch,cox-cei/ansible-role-proWatch,None,True,,2023-11-08T16:15:06Z,2024-10-18T19:45:12Z,2023-12-01T20:55:57Z,11,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-proWatch,https://github.com/cox-cei/ansible-role-proWatch.git
ansible-role-qualys,cox-cei/ansible-role-qualys,None,True,,2023-11-08T16:14:13Z,2024-10-18T19:45:44Z,2024-04-03T20:01:54Z,20241,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-qualys,https://github.com/cox-cei/ansible-role-qualys.git
ansible-role-rds,cox-cei/ansible-role-rds,None,True,,2023-11-08T16:14:08Z,2025-01-08T19:49:21Z,2025-01-08T19:49:12Z,9,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-rds,https://github.com/cox-cei/ansible-role-rds.git
ansible-role-removeIpaHost,cox-cei/ansible-role-removeIpaHost,None,True,,2023-11-08T16:14:06Z,2024-10-18T19:46:27Z,2024-10-01T14:03:40Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-removeIpaHost,https://github.com/cox-cei/ansible-role-removeIpaHost.git
ansible-role-rsyslog,cox-cei/ansible-role-rsyslog,None,True,,2023-11-08T16:15:08Z,2024-10-18T19:46:20Z,2023-12-01T20:56:30Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-rsyslog,https://github.com/cox-cei/ansible-role-rsyslog.git
ansible-role-sailPoint,cox-cei/ansible-role-sailPoint,None,True,,2023-11-08T16:13:19Z,2024-10-18T19:47:00Z,2023-12-01T21:08:17Z,1229,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-sailPoint,https://github.com/cox-cei/ansible-role-sailPoint.git
ansible-role-satelliteHost,cox-cei/ansible-role-satelliteHost,None,True,,2023-11-08T16:14:04Z,2024-10-18T19:47:09Z,2024-08-15T14:55:54Z,8,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-satelliteHost,https://github.com/cox-cei/ansible-role-satelliteHost.git
ansible-role-sccm,cox-cei/ansible-role-sccm,None,True,,2023-11-08T16:16:00Z,2025-01-06T20:00:54Z,2025-01-08T19:36:54Z,2180,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-sccm,https://github.com/cox-cei/ansible-role-sccm.git
ansible-role-serviceNowBuildInventory,cox-cei/ansible-role-serviceNowBuildInventory,None,True,,2023-11-08T16:15:58Z,2024-10-18T19:47:50Z,2023-12-01T20:41:22Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-serviceNowBuildInventory,https://github.com/cox-cei/ansible-role-serviceNowBuildInventory.git
ansible-role-sonarQube,cox-cei/ansible-role-sonarQube,None,True,,2023-11-08T16:17:43Z,2024-10-18T19:48:30Z,2023-11-28T17:24:06Z,17,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-sonarQube,https://github.com/cox-cei/ansible-role-sonarQube.git
ansible-role-syncTime,cox-cei/ansible-role-syncTime,None,True,,2023-11-08T16:18:35Z,2024-10-18T19:48:37Z,2023-11-28T16:15:34Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-syncTime,https://github.com/cox-cei/ansible-role-syncTime.git
ansible-role-template,cox-cei/ansible-role-template,None,True,,2023-11-08T16:13:11Z,2024-10-18T19:49:02Z,2023-12-01T21:06:21Z,5,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-template,https://github.com/cox-cei/ansible-role-template.git
ansible-role-templatizeVm,cox-cei/ansible-role-templatizeVm,None,True,,2023-11-08T16:13:21Z,2024-10-18T19:49:15Z,2023-12-01T21:08:52Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-templatizeVm,https://github.com/cox-cei/ansible-role-templatizeVm.git
ansible-role-tightVNC,cox-cei/ansible-role-tightVNC,None,True,,2023-11-08T16:18:31Z,2024-10-18T19:49:37Z,2023-11-28T16:16:17Z,1932,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-tightVNC,https://github.com/cox-cei/ansible-role-tightVNC.git
ansible-role-tomcat,cox-cei/ansible-role-tomcat,None,True,,2023-11-08T16:15:01Z,2024-10-18T19:49:51Z,2023-12-01T20:54:56Z,7,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-tomcat,https://github.com/cox-cei/ansible-role-tomcat.git
ansible-role-treeSize,cox-cei/ansible-role-treeSize,None,True,,2023-11-08T16:17:45Z,2024-10-18T19:50:12Z,2023-11-28T17:21:42Z,2407,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-treeSize,https://github.com/cox-cei/ansible-role-treeSize.git
ansible-role-uiPath,cox-cei/ansible-role-uiPath,None,True,Batchfile,2023-11-08T16:16:31Z,2025-01-08T19:51:22Z,2025-01-08T19:57:25Z,19,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-uiPath,https://github.com/cox-cei/ansible-role-uiPath.git
ansible-role-vertex,cox-cei/ansible-role-vertex,None,True,Jinja,2023-11-08T16:16:36Z,2024-10-18T19:50:51Z,2024-06-27T20:54:45Z,32,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-vertex,https://github.com/cox-cei/ansible-role-vertex.git
ansible-role-vmSetup,cox-cei/ansible-role-vmSetup,None,True,,2023-11-08T16:16:44Z,2025-01-24T20:09:06Z,2025-01-24T20:11:08Z,27,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-vmSetup,https://github.com/cox-cei/ansible-role-vmSetup.git
ansible-role-vmwareChangeVmName,cox-cei/ansible-role-vmwareChangeVmName,None,True,,2023-11-08T16:18:18Z,2024-10-18T19:51:31Z,2023-11-28T17:16:15Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-vmwareChangeVmName,https://github.com/cox-cei/ansible-role-vmwareChangeVmName.git
ansible-role-vmwareCloneTemplates,cox-cei/ansible-role-vmwareCloneTemplates,None,True,,2023-11-08T16:15:04Z,2024-10-18T19:51:48Z,2023-12-01T20:55:28Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-vmwareCloneTemplates,https://github.com/cox-cei/ansible-role-vmwareCloneTemplates.git
ansible-role-vmwareConvertVmToTemplate,cox-cei/ansible-role-vmwareConvertVmToTemplate,None,True,,2023-11-08T16:14:52Z,2024-10-18T19:52:12Z,2023-12-01T20:47:56Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-vmwareConvertVmToTemplate,https://github.com/cox-cei/ansible-role-vmwareConvertVmToTemplate.git
ansible-role-vmwareCreateVmFromTemplate,cox-cei/ansible-role-vmwareCreateVmFromTemplate,None,True,,2023-11-08T16:15:41Z,2024-10-18T19:52:35Z,2023-12-01T20:43:29Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-vmwareCreateVmFromTemplate,https://github.com/cox-cei/ansible-role-vmwareCreateVmFromTemplate.git
ansible-role-vmwareRemoveVm,cox-cei/ansible-role-vmwareRemoveVm,None,True,,2023-11-08T16:13:15Z,2024-10-18T19:52:53Z,2023-12-01T21:07:20Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-vmwareRemoveVm,https://github.com/cox-cei/ansible-role-vmwareRemoveVm.git
ansible-role-waitAvailable,cox-cei/ansible-role-waitAvailable,None,True,,2023-11-08T16:17:39Z,2024-10-18T19:53:29Z,2023-11-28T17:24:41Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-waitAvailable,https://github.com/cox-cei/ansible-role-waitAvailable.git
ansible-role-webCenterImaging,cox-cei/ansible-role-webCenterImaging,None,True,Jinja,2023-12-12T14:20:55Z,2025-06-16T15:16:44Z,2025-06-16T15:16:40Z,53,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-webCenterImaging,https://github.com/cox-cei/ansible-role-webCenterImaging.git
ansible-role-webCenterOhs,cox-cei/ansible-role-webCenterOhs,None,True,Jinja,2023-12-12T14:20:38Z,2025-06-16T15:17:40Z,2025-06-16T15:17:36Z,50,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-webCenterOhs,https://github.com/cox-cei/ansible-role-webCenterOhs.git
ansible-role-webdeploy,cox-cei/ansible-role-webdeploy,None,True,,2023-11-08T16:14:00Z,2024-10-18T19:54:19Z,2023-12-01T20:59:10Z,7,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-webdeploy,https://github.com/cox-cei/ansible-role-webdeploy.git
ansible-role-windows,cox-cei/ansible-role-windows,None,True,,2023-11-08T16:13:13Z,2025-06-23T18:48:41Z,2025-06-23T18:48:39Z,23,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-windows,https://github.com/cox-cei/ansible-role-windows.git
ansible-role-winOpenSSH,cox-cei/ansible-role-winOpenSSH,None,True,PowerShell,2024-02-02T17:18:56Z,2024-10-18T19:54:52Z,2024-02-22T15:30:20Z,12,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-winOpenSSH,https://github.com/cox-cei/ansible-role-winOpenSSH.git
ansible-role-zscalerAppBigFix,cox-cei/ansible-role-zscalerAppBigFix,None,True,,2023-12-12T14:20:31Z,2024-10-18T19:55:29Z,2023-12-12T20:03:48Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-zscalerAppBigFix,https://github.com/cox-cei/ansible-role-zscalerAppBigFix.git
ansible-role-zscalerAppConnector,cox-cei/ansible-role-zscalerAppConnector,None,True,,2023-12-12T14:21:02Z,2024-10-18T19:55:31Z,2023-12-12T20:25:04Z,5,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-zscalerAppConnector,https://github.com/cox-cei/ansible-role-zscalerAppConnector.git
ansible-role-zscalerAppLog,cox-cei/ansible-role-zscalerAppLog,None,True,,2023-12-12T14:20:41Z,2024-10-18T19:56:10Z,2023-12-12T20:20:13Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-role-zscalerAppLog,https://github.com/cox-cei/ansible-role-zscalerAppLog.git
ansible-workspace-batch,cox-cei/ansible-workspace-batch,None,True,,2024-06-27T16:35:02Z,2024-10-18T22:48:10Z,2024-08-13T13:47:18Z,48,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-workspace-batch,https://github.com/cox-cei/ansible-workspace-batch.git
ansible-workspace-ebs,cox-cei/ansible-workspace-ebs,None,True,,2024-06-27T16:35:00Z,2024-10-18T22:48:44Z,2024-06-27T17:22:15Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-workspace-ebs,https://github.com/cox-cei/ansible-workspace-ebs.git
ansible-workspace-linux,cox-cei/ansible-workspace-linux,None,True,,2024-06-27T17:16:42Z,2024-10-18T22:48:49Z,2024-06-27T17:21:39Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-workspace-linux,https://github.com/cox-cei/ansible-workspace-linux.git
ansible-workspace-monitoring,cox-cei/ansible-workspace-monitoring,None,True,,2024-06-27T16:34:59Z,2024-10-18T22:49:26Z,2025-01-30T18:54:51Z,82,0,0,0,0,main,False,False,https://github.com/cox-cei/ansible-workspace-monitoring,https://github.com/cox-cei/ansible-workspace-monitoring.git
ansible-workspace-platformAutomation,cox-cei/ansible-workspace-platformAutomation,None,True,,2023-11-08T16:13:09Z,2025-06-23T18:49:57Z,2025-06-23T19:24:14Z,182647,0,0,0,1,main,False,False,https://github.com/cox-cei/ansible-workspace-platformAutomation,https://github.com/cox-cei/ansible-workspace-platformAutomation.git
apigee-pipeline-gcp,cox-cei/apigee-pipeline-gcp,,True,,2024-11-26T21:08:13Z,2025-01-01T20:00:37Z,2025-01-01T20:00:34Z,196,0,0,0,0,dev,False,False,https://github.com/cox-cei/apigee-pipeline-gcp,https://github.com/cox-cei/apigee-pipeline-gcp.git
apigeex-cci-fin-hr,cox-cei/apigeex-cci-fin-hr,,True,,2024-11-22T23:06:43Z,2025-06-30T13:13:22Z,2025-06-30T13:13:20Z,44,0,0,0,0,dev,False,False,https://github.com/cox-cei/apigeex-cci-fin-hr,https://github.com/cox-cei/apigeex-cci-fin-hr.git
apigeex-cci-fin-supplier-portal,cox-cei/apigeex-cci-fin-supplier-portal,,True,,2024-11-22T23:05:46Z,2025-06-30T13:12:09Z,2025-06-30T13:12:07Z,43,0,0,0,0,dev,False,False,https://github.com/cox-cei/apigeex-cci-fin-supplier-portal,https://github.com/cox-cei/apigeex-cci-fin-supplier-portal.git
apigeex-cci-sc-3pl,cox-cei/apigeex-cci-sc-3pl,,True,JavaScript,2024-11-22T23:04:41Z,2025-06-30T13:14:28Z,2025-06-30T13:14:26Z,36,0,0,0,1,dev,False,False,https://github.com/cox-cei/apigeex-cci-sc-3pl,https://github.com/cox-cei/apigeex-cci-sc-3pl.git
apigeex-cci-sc-3pl-procure,cox-cei/apigeex-cci-sc-3pl-procure,,True,,2024-11-22T23:04:20Z,2025-06-30T13:13:42Z,2025-06-30T13:13:40Z,39,0,0,0,2,dev,False,False,https://github.com/cox-cei/apigeex-cci-sc-3pl-procure,https://github.com/cox-cei/apigeex-cci-sc-3pl-procure.git
apigeex-cci-sc-3pl-wireless,cox-cei/apigeex-cci-sc-3pl-wireless,,True,,2024-11-22T23:03:50Z,2025-06-30T13:14:55Z,2025-06-30T13:14:53Z,38,0,0,0,1,dev,False,False,https://github.com/cox-cei/apigeex-cci-sc-3pl-wireless,https://github.com/cox-cei/apigeex-cci-sc-3pl-wireless.git
apigeex-cci-sc-high-availability,cox-cei/apigeex-cci-sc-high-availability,,True,,2024-11-22T23:04:18Z,2025-06-30T13:12:38Z,2025-06-30T13:12:37Z,31,0,0,0,0,dev,False,False,https://github.com/cox-cei/apigeex-cci-sc-high-availability,https://github.com/cox-cei/apigeex-cci-sc-high-availability.git
apigeex-cci-sc-network-fulfillment,cox-cei/apigeex-cci-sc-network-fulfillment,,True,JavaScript,2024-11-22T23:04:31Z,2025-06-30T13:01:12Z,2025-06-30T13:01:09Z,67,0,0,0,0,dev,False,False,https://github.com/cox-cei/apigeex-cci-sc-network-fulfillment,https://github.com/cox-cei/apigeex-cci-sc-network-fulfillment.git
apigeex-cci-sc-servicenow,cox-cei/apigeex-cci-sc-servicenow,,True,,2024-11-22T23:04:53Z,2025-06-30T13:00:39Z,2025-06-30T13:00:37Z,31,0,0,0,0,dev,False,False,https://github.com/cox-cei/apigeex-cci-sc-servicenow,https://github.com/cox-cei/apigeex-cci-sc-servicenow.git
apigeex-cci-sc-wireless,cox-cei/apigeex-cci-sc-wireless,,True,JavaScript,2024-11-22T23:05:03Z,2025-06-30T10:14:48Z,2025-07-01T17:10:51Z,103,0,0,0,7,dev,False,False,https://github.com/cox-cei/apigeex-cci-sc-wireless,https://github.com/cox-cei/apigeex-cci-sc-wireless.git
apigeex-cei-apix-poc,cox-cei/apigeex-cei-apix-poc,,True,,2024-11-22T23:03:14Z,2024-12-03T14:41:32Z,2024-12-03T14:34:50Z,1,0,0,0,0,main,False,False,https://github.com/cox-cei/apigeex-cei-apix-poc,https://github.com/cox-cei/apigeex-cei-apix-poc.git
apigeex-cei-fin-payables,cox-cei/apigeex-cei-fin-payables,,True,,2024-11-22T23:01:04Z,2025-06-20T12:22:31Z,2025-06-20T12:22:29Z,85,0,0,0,16,dev,False,False,https://github.com/cox-cei/apigeex-cei-fin-payables,https://github.com/cox-cei/apigeex-cei-fin-payables.git
apigeex-cei-fin-project-accounting,cox-cei/apigeex-cei-fin-project-accounting,,True,,2024-11-22T23:01:26Z,2025-06-30T13:13:00Z,2025-06-30T13:12:58Z,31,0,0,0,0,dev,False,False,https://github.com/cox-cei/apigeex-cei-fin-project-accounting,https://github.com/cox-cei/apigeex-cei-fin-project-accounting.git
apigeex-cei-sc-3pl,cox-cei/apigeex-cei-sc-3pl,,True,,2024-11-22T23:05:17Z,2025-06-30T14:22:52Z,2025-06-30T14:22:49Z,39,0,0,0,1,dev,False,False,https://github.com/cox-cei/apigeex-cei-sc-3pl,https://github.com/cox-cei/apigeex-cei-sc-3pl.git
apigeex-cei-sc-network-fulfillment,cox-cei/apigeex-cei-sc-network-fulfillment,,True,,2024-11-22T23:03:00Z,2025-06-30T13:01:33Z,2025-06-30T13:01:30Z,54,0,0,0,0,dev,False,False,https://github.com/cox-cei/apigeex-cei-sc-network-fulfillment,https://github.com/cox-cei/apigeex-cei-sc-network-fulfillment.git
apigeex-cei-sc-wireless,cox-cei/apigeex-cei-sc-wireless,,True,,2024-11-22T23:05:56Z,2025-06-30T13:14:04Z,2025-06-30T13:14:02Z,39,0,0,0,0,dev,False,False,https://github.com/cox-cei/apigeex-cei-sc-wireless,https://github.com/cox-cei/apigeex-cei-sc-wireless.git
apigeex-CICD-Central,cox-cei/apigeex-CICD-Central,Central Repo for APIGEE CI/CD Pipeline,True,Python,2024-12-16T14:21:16Z,2025-07-02T12:53:51Z,2025-07-02T12:53:47Z,582,0,0,0,0,main,False,False,https://github.com/cox-cei/apigeex-CICD-Central,https://github.com/cox-cei/apigeex-CICD-Central.git
apigeex-pipeline-gcp,cox-cei/apigeex-pipeline-gcp,Repository for APIGEE,True,,2024-11-26T21:06:55Z,2024-11-26T21:06:56Z,2024-11-26T21:06:55Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/apigeex-pipeline-gcp,https://github.com/cox-cei/apigeex-pipeline-gcp.git
apigeex-test-dev,cox-cei/apigeex-test-dev,Test repo for branch principle,True,,2024-12-10T20:11:52Z,2024-12-30T09:50:56Z,2024-12-30T09:50:57Z,94,0,0,0,1,dev,False,False,https://github.com/cox-cei/apigeex-test-dev,https://github.com/cox-cei/apigeex-test-dev.git
apptio-targetprocess,cox-cei/apptio-targetprocess,Automation rules and mashups related to CEI Apptio Targetprocess,True,,2024-06-04T18:34:06Z,2025-03-20T14:32:53Z,2025-07-11T14:35:57Z,167,0,0,0,1,main,False,False,https://github.com/cox-cei/apptio-targetprocess,https://github.com/cox-cei/apptio-targetprocess.git
Azure-CoxIntranet-API,cox-cei/Azure-CoxIntranet-API,None,True,C#,2024-08-27T21:01:28Z,2025-02-12T19:14:15Z,2024-09-20T21:10:57Z,34,1,1,0,0,main,False,False,https://github.com/cox-cei/Azure-CoxIntranet-API,https://github.com/cox-cei/Azure-CoxIntranet-API.git
Azure-CoxIntranet-Automations,cox-cei/Azure-CoxIntranet-Automations,None,True,PowerShell,2024-08-27T22:00:07Z,2025-06-27T21:24:27Z,2025-07-08T19:07:57Z,20810,1,1,0,1,main,False,False,https://github.com/cox-cei/Azure-CoxIntranet-Automations,https://github.com/cox-cei/Azure-CoxIntranet-Automations.git
azure-dotnet-cimmodules,cox-cei/azure-dotnet-cimmodules,None,True,C#,2023-12-19T18:27:21Z,2024-10-18T19:56:53Z,2024-06-13T13:30:28Z,11563,0,0,0,3,master,False,False,https://github.com/cox-cei/azure-dotnet-cimmodules,https://github.com/cox-cei/azure-dotnet-cimmodules.git
azure-fabric-dq-governance,cox-cei/azure-fabric-dq-governance,,True,,2025-08-01T13:40:55Z,2025-08-01T13:40:57Z,2025-08-01T13:40:55Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/azure-fabric-dq-governance,https://github.com/cox-cei/azure-fabric-dq-governance.git
azure-html-coxhackathon,cox-cei/azure-html-coxhackathon,None,True,HTML,2024-03-04T21:15:50Z,2024-10-18T19:57:01Z,2024-03-04T21:54:38Z,7950,0,0,0,0,main,False,False,https://github.com/cox-cei/azure-html-coxhackathon,https://github.com/cox-cei/azure-html-coxhackathon.git
azure-html-icxoutage,cox-cei/azure-html-icxoutage,None,True,HTML,2024-03-04T21:16:02Z,2024-10-18T19:57:32Z,2024-09-09T12:41:58Z,1644,0,0,0,0,main,False,False,https://github.com/cox-cei/azure-html-icxoutage,https://github.com/cox-cei/azure-html-icxoutage.git
azure-resourceCostCalc-application,cox-cei/azure-resourceCostCalc-application,,True,Python,2024-11-22T18:32:49Z,2024-11-22T18:34:07Z,2024-11-22T18:34:03Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/azure-resourceCostCalc-application,https://github.com/cox-cei/azure-resourceCostCalc-application.git
azure-sandboxcleanup-application,cox-cei/azure-sandboxcleanup-application,Azure Sandbox Resources Cleanup Script,True,PowerShell,2024-09-23T16:37:23Z,2024-11-25T14:46:42Z,2024-11-25T14:45:50Z,38,0,0,0,0,main,False,False,https://github.com/cox-cei/azure-sandboxcleanup-application,https://github.com/cox-cei/azure-sandboxcleanup-application.git
azure-securityReporting,cox-cei/azure-securityReporting,None,True,PowerShell,2023-11-13T20:35:52Z,2024-11-25T19:56:06Z,2024-11-25T19:56:04Z,43,0,0,0,0,main,False,False,https://github.com/cox-cei/azure-securityReporting,https://github.com/cox-cei/azure-securityReporting.git
azure-storybook,cox-cei/azure-storybook,None,True,JavaScript,2024-01-17T21:26:03Z,2024-10-18T19:58:18Z,2024-07-09T14:23:52Z,5697,0,0,0,9,master,False,False,https://github.com/cox-cei/azure-storybook,https://github.com/cox-cei/azure-storybook.git
azure-web-ceiibtbootcamp2025,cox-cei/azure-web-ceiibtbootcamp2025,2025 CEI IBT Intern Bootcamp,True,JavaScript,2025-06-20T15:31:35Z,2025-08-01T18:17:03Z,2025-08-01T18:17:01Z,4740,1,1,0,4,main,False,False,https://github.com/cox-cei/azure-web-ceiibtbootcamp2025,https://github.com/cox-cei/azure-web-ceiibtbootcamp2025.git
azure-webapp-34by34,cox-cei/azure-webapp-34by34,None,True,JavaScript,2023-11-09T16:01:26Z,2024-12-19T22:53:10Z,2025-08-04T03:06:05Z,1851,0,0,0,1,main,False,False,https://github.com/cox-cei/azure-webapp-34by34,https://github.com/cox-cei/azure-webapp-34by34.git
azure-webapp-DigitalPatentDisplay,cox-cei/azure-webapp-DigitalPatentDisplay,Repository for the Digital Patent Display in C-tech building,True,Vue,2024-11-04T19:43:26Z,2025-07-21T17:20:49Z,2025-07-24T15:45:42Z,331594,0,0,0,0,develop,False,False,https://github.com/cox-cei/azure-webapp-DigitalPatentDisplay,https://github.com/cox-cei/azure-webapp-DigitalPatentDisplay.git
azure-wordpress-cerf2022,cox-cei/azure-wordpress-cerf2022,None,True,PHP,2024-03-04T21:15:59Z,2024-10-18T19:58:56Z,2024-03-04T22:00:46Z,131307,0,0,0,0,main,False,False,https://github.com/cox-cei/azure-wordpress-cerf2022,https://github.com/cox-cei/azure-wordpress-cerf2022.git
azure-wordpress-coxalert,cox-cei/azure-wordpress-coxalert,azure coxalert.com code archive,True,,2024-02-13T18:39:50Z,2024-07-21T03:35:18Z,2024-02-13T18:39:50Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/azure-wordpress-coxalert,https://github.com/cox-cei/azure-wordpress-coxalert.git
azure-wordpress-coxcodeofconduct,cox-cei/azure-wordpress-coxcodeofconduct,None,True,PHP,2023-11-03T13:27:45Z,2024-10-18T19:59:41Z,2023-11-10T17:12:46Z,1152653,0,0,0,0,main,False,False,https://github.com/cox-cei/azure-wordpress-coxcodeofconduct,https://github.com/cox-cei/azure-wordpress-coxcodeofconduct.git
azure-wordpress-coxrelief,cox-cei/azure-wordpress-coxrelief,None,True,,2023-12-06T17:38:49Z,2024-10-18T20:00:04Z,2023-12-06T17:38:49Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/azure-wordpress-coxrelief,https://github.com/cox-cei/azure-wordpress-coxrelief.git
bmc-photo-vetting-tool-deprecated,cox-cei/bmc-photo-vetting-tool-deprecated,None,True,C#,2023-07-12T13:44:43Z,2024-10-18T20:00:22Z,2023-07-12T13:56:28Z,38060,0,0,0,0,master,False,False,https://github.com/cox-cei/bmc-photo-vetting-tool-deprecated,https://github.com/cox-cei/bmc-photo-vetting-tool-deprecated.git
Bootcamp-2024,cox-cei/Bootcamp-2024,Repository for 2024 Summer Intern Bootcamp Project.,True,Python,2024-06-24T18:29:28Z,2024-07-25T20:57:19Z,2024-07-30T12:16:15Z,41402,0,0,0,8,dev,False,False,https://github.com/cox-cei/Bootcamp-2024,https://github.com/cox-cei/Bootcamp-2024.git
campus-traffic-cam-deprecated,cox-cei/campus-traffic-cam-deprecated,None,True,HTML,2023-07-12T17:13:02Z,2024-10-18T20:00:43Z,2023-07-12T17:15:22Z,648,0,0,0,0,master,False,False,https://github.com/cox-cei/campus-traffic-cam-deprecated,https://github.com/cox-cei/campus-traffic-cam-deprecated.git
CEI-EBS-DATAFIX,cox-cei/CEI-EBS-DATAFIX,None,True,,2023-09-11T13:41:17Z,2024-10-18T20:01:01Z,2023-09-11T13:41:18Z,0,0,0,0,0,master,False,False,https://github.com/cox-cei/CEI-EBS-DATAFIX,https://github.com/cox-cei/CEI-EBS-DATAFIX.git
CEI-EBS-XXCCI,cox-cei/CEI-EBS-XXCCI,None,True,,2023-09-11T13:41:22Z,2024-10-18T20:01:24Z,2023-09-11T13:41:22Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-EBS-XXCCI,https://github.com/cox-cei/CEI-EBS-XXCCI.git
CEI-EBS-XXCEI,cox-cei/CEI-EBS-XXCEI,None,True,Rich Text Format,2023-07-10T20:16:41Z,2024-10-18T20:01:42Z,2024-03-13T15:07:15Z,207036,0,0,0,2,master,False,False,https://github.com/cox-cei/CEI-EBS-XXCEI,https://github.com/cox-cei/CEI-EBS-XXCEI.git
CEI-EBS-XXCMG,cox-cei/CEI-EBS-XXCMG,None,True,,2023-09-11T13:41:15Z,2024-10-18T20:01:59Z,2023-09-11T13:41:16Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-EBS-XXCMG,https://github.com/cox-cei/CEI-EBS-XXCMG.git
CEI-IA-Common-Library,cox-cei/CEI-IA-Common-Library,Common utility workflows used in many automations.,True,C#,2025-06-11T14:44:28Z,2025-07-28T16:53:31Z,2025-07-28T16:53:27Z,18,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-IA-Common-Library,https://github.com/cox-cei/CEI-IA-Common-Library.git
CEI-IA-Excel-Common-Library,cox-cei/CEI-IA-Excel-Common-Library,Utility workflows for Excel used across multiple automations,True,,2025-06-11T14:43:41Z,2025-06-11T14:43:43Z,2025-06-11T14:43:41Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-IA-Excel-Common-Library,https://github.com/cox-cei/CEI-IA-Excel-Common-Library.git
CEI-IA-SharePoint-Common-Library,cox-cei/CEI-IA-SharePoint-Common-Library,Utility workflows for SharePoint used across multiple automations,True,,2025-06-11T14:44:25Z,2025-06-11T14:44:26Z,2025-06-11T14:44:25Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-IA-SharePoint-Common-Library,https://github.com/cox-cei/CEI-IA-SharePoint-Common-Library.git
CEI-IA-SSO-Library,cox-cei/CEI-IA-SSO-Library,Internal UiPath Library for connecting to SSO applications,True,,2025-07-01T20:01:50Z,2025-07-02T20:45:05Z,2025-07-02T21:47:31Z,1012,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-IA-SSO-Library,https://github.com/cox-cei/CEI-IA-SSO-Library.git
CEI-Internal-Oracle-InvoiceWorkbench,cox-cei/CEI-Internal-Oracle-InvoiceWorkbench,,True,,2025-01-10T17:03:05Z,2025-07-17T16:05:33Z,2025-07-18T14:24:26Z,12358,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-Internal-Oracle-InvoiceWorkbench,https://github.com/cox-cei/CEI-Internal-Oracle-InvoiceWorkbench.git
cei-platform-github-actions,cox-cei/cei-platform-github-actions,None,True,,2023-05-23T15:53:50Z,2024-10-18T20:02:18Z,2024-02-14T18:56:06Z,5,0,0,0,0,main,False,False,https://github.com/cox-cei/cei-platform-github-actions,https://github.com/cox-cei/cei-platform-github-actions.git
CEI-RPA-CSC_CallSummarization_PIIMaskingModel,cox-cei/CEI-RPA-CSC_CallSummarization_PIIMaskingModel,,True,,2025-04-07T20:21:38Z,2025-04-07T20:21:40Z,2025-04-07T20:21:39Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-RPA-CSC_CallSummarization_PIIMaskingModel,https://github.com/cox-cei/CEI-RPA-CSC_CallSummarization_PIIMaskingModel.git
CEI-RPA-CSC_CallSummarization_SummarizationModel,cox-cei/CEI-RPA-CSC_CallSummarization_SummarizationModel,,True,,2025-04-07T20:21:52Z,2025-04-07T20:21:54Z,2025-04-07T20:21:52Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-RPA-CSC_CallSummarization_SummarizationModel,https://github.com/cox-cei/CEI-RPA-CSC_CallSummarization_SummarizationModel.git
CEI-RPA-SustainabilityCloud-Dispatcher,cox-cei/CEI-RPA-SustainabilityCloud-Dispatcher,,True,,2024-12-12T14:29:40Z,2025-01-03T16:05:40Z,2025-01-03T16:05:36Z,1085,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Dispatcher,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Dispatcher.git
CEI-RPA-SustainabilityCloud-Document-Performer,cox-cei/CEI-RPA-SustainabilityCloud-Document-Performer,,True,,2024-12-12T14:30:54Z,2025-03-10T17:43:08Z,2025-03-10T17:43:04Z,10240,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Document-Performer,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Document-Performer.git
CEI-RPA-SustainabilityCloud-Output-Performer,cox-cei/CEI-RPA-SustainabilityCloud-Output-Performer,,True,Batchfile,2024-12-12T14:30:25Z,2025-01-03T15:52:24Z,2025-01-03T15:52:21Z,9906,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Output-Performer,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Output-Performer.git
CEI-RPA-SustainabilityCloud-Webcenter-Performer,cox-cei/CEI-RPA-SustainabilityCloud-Webcenter-Performer,,True,,2024-12-12T14:30:43Z,2025-06-26T14:13:16Z,2025-06-26T14:13:11Z,1325,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Webcenter-Performer,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Webcenter-Performer.git
CEI-WEBCENTER-ALL-ORGS-NONSOA,cox-cei/CEI-WEBCENTER-ALL-ORGS-NONSOA,None,True,,2023-07-13T20:08:21Z,2024-10-18T20:02:39Z,2023-07-13T20:08:21Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-WEBCENTER-ALL-ORGS-NONSOA,https://github.com/cox-cei/CEI-WEBCENTER-ALL-ORGS-NONSOA.git
CEI-WEBCENTER-CCI-SOA,cox-cei/CEI-WEBCENTER-CCI-SOA,None,True,,2023-09-11T13:41:13Z,2024-10-18T20:02:53Z,2023-09-11T13:41:14Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-WEBCENTER-CCI-SOA,https://github.com/cox-cei/CEI-WEBCENTER-CCI-SOA.git
CEI-WEBCENTER-CEI-SOA,cox-cei/CEI-WEBCENTER-CEI-SOA,None,True,,2023-09-11T13:41:20Z,2024-10-18T20:03:25Z,2023-09-11T13:41:20Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-WEBCENTER-CEI-SOA,https://github.com/cox-cei/CEI-WEBCENTER-CEI-SOA.git
CEI-WEBCENTER-CMG-SOA,cox-cei/CEI-WEBCENTER-CMG-SOA,None,True,,2023-09-11T13:41:11Z,2024-10-18T20:03:34Z,2023-09-11T13:41:11Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-WEBCENTER-CMG-SOA,https://github.com/cox-cei/CEI-WEBCENTER-CMG-SOA.git
cisco-webex-esoc-kiosk,cox-cei/cisco-webex-esoc-kiosk,This repository houses the code for the CEI Corporate Security kiosk for Cisco video devices.,True,HTML,2025-01-29T20:56:02Z,2025-01-30T19:21:03Z,2025-01-30T19:21:00Z,1049,0,0,0,0,main,False,False,https://github.com/cox-cei/cisco-webex-esoc-kiosk,https://github.com/cox-cei/cisco-webex-esoc-kiosk.git
Cloud-IDMC-Enterprise-Integrations,cox-cei/Cloud-IDMC-Enterprise-Integrations,None,True,Python,2024-04-26T14:19:01Z,2024-10-18T22:50:09Z,2025-06-13T15:55:16Z,3398,0,0,0,0,main,False,False,https://github.com/cox-cei/Cloud-IDMC-Enterprise-Integrations,https://github.com/cox-cei/Cloud-IDMC-Enterprise-Integrations.git
Comms-Archive,cox-cei/Comms-Archive,Website Archive,True,HTML,2023-08-07T14:11:01Z,2024-07-21T23:20:42Z,2023-08-07T14:11:10Z,1336,0,0,0,0,master,False,False,https://github.com/cox-cei/Comms-Archive,https://github.com/cox-cei/Comms-Archive.git
corporateservices-datalakehouse-fabric,cox-cei/corporateservices-datalakehouse-fabric, CorporateServices Repo for Fabric workspaces,True,,2025-01-15T18:59:20Z,2025-01-16T20:25:35Z,2025-07-21T13:26:47Z,269,0,0,0,0,main,False,False,https://github.com/cox-cei/corporateservices-datalakehouse-fabric,https://github.com/cox-cei/corporateservices-datalakehouse-fabric.git
CorporateServicesdatalakehouse-data-factory,cox-cei/CorporateServicesdatalakehouse-data-factory,CorporateServices Repo for Data Factory and deployment pipeline,True,,2024-10-17T21:20:15Z,2025-02-11T15:35:17Z,2025-02-11T15:35:14Z,287,0,0,0,0,main,False,False,https://github.com/cox-cei/CorporateServicesdatalakehouse-data-factory,https://github.com/cox-cei/CorporateServicesdatalakehouse-data-factory.git
CorporateServicesdatalakehouse-meta-database,cox-cei/CorporateServicesdatalakehouse-meta-database,CorporateServices Repo for the meta database project and deployment pipeline,True,TSQL,2024-10-17T21:20:13Z,2025-07-22T15:38:18Z,2025-07-25T17:21:58Z,129,0,0,0,0,main,False,False,https://github.com/cox-cei/CorporateServicesdatalakehouse-meta-database,https://github.com/cox-cei/CorporateServicesdatalakehouse-meta-database.git
CorporateServicesdatalakehouse-tf-deployment,cox-cei/CorporateServicesdatalakehouse-tf-deployment,CorporateServices Repo for terraform deployment of Azure infrastructure as code,True,HCL,2024-10-17T21:20:11Z,2024-12-05T20:38:04Z,2024-12-05T20:38:02Z,38,0,0,0,0,main,False,False,https://github.com/cox-cei/CorporateServicesdatalakehouse-tf-deployment,https://github.com/cox-cei/CorporateServicesdatalakehouse-tf-deployment.git
COX-REFramework-Dispatcher,cox-cei/COX-REFramework-Dispatcher,A COX baked Dispatcher based off of UiPath's REFramework ,True,HTML,2024-05-15T15:25:46Z,2024-11-14T22:08:20Z,2024-11-14T22:08:17Z,976,0,0,0,0,main,False,False,https://github.com/cox-cei/COX-REFramework-Dispatcher,https://github.com/cox-cei/COX-REFramework-Dispatcher.git
COX-REFramework-Performer,cox-cei/COX-REFramework-Performer,A COX baked Performer based off of UiPath's REFramework,True,HTML,2024-05-15T15:25:44Z,2024-11-25T13:34:06Z,2025-03-10T20:00:59Z,95790,0,0,0,1,main,False,False,https://github.com/cox-cei/COX-REFramework-Performer,https://github.com/cox-cei/COX-REFramework-Performer.git
coxconserves-data-factory,cox-cei/coxconserves-data-factory,None,True,,2024-08-09T14:42:11Z,2025-07-31T20:42:38Z,2025-08-03T03:56:01Z,502,0,0,0,0,main,False,False,https://github.com/cox-cei/coxconserves-data-factory,https://github.com/cox-cei/coxconserves-data-factory.git
coxconserves-fabric-dc-workspace,cox-cei/coxconserves-fabric-dc-workspace,Cox Conserves Data Consumer Fabric Workspace repo,True,Python,2025-03-09T20:09:51Z,2025-06-30T20:22:27Z,2025-06-30T20:22:24Z,31,0,0,0,0,main,False,False,https://github.com/cox-cei/coxconserves-fabric-dc-workspace,https://github.com/cox-cei/coxconserves-fabric-dc-workspace.git
coxconserves-fabric-workspace,cox-cei/coxconserves-fabric-workspace,None,True,Python,2024-08-09T14:42:07Z,2025-01-16T21:45:39Z,2025-08-03T11:24:30Z,1328,0,0,0,0,main,False,False,https://github.com/cox-cei/coxconserves-fabric-workspace,https://github.com/cox-cei/coxconserves-fabric-workspace.git
coxconserves-meta-database,cox-cei/coxconserves-meta-database,None,True,TSQL,2024-08-09T14:42:05Z,2025-07-30T16:12:05Z,2025-07-30T16:12:02Z,256,0,0,0,0,main,False,False,https://github.com/cox-cei/coxconserves-meta-database,https://github.com/cox-cei/coxconserves-meta-database.git
coxconserves-tf-deployment,cox-cei/coxconserves-tf-deployment,None,True,HCL,2024-08-09T14:42:03Z,2024-12-05T20:46:59Z,2024-12-05T20:46:56Z,26371,0,0,0,0,main,False,False,https://github.com/cox-cei/coxconserves-tf-deployment,https://github.com/cox-cei/coxconserves-tf-deployment.git
CoxConservesAIAgent,cox-cei/CoxConservesAIAgent,,True,,2025-06-09T13:18:07Z,2025-06-09T13:18:09Z,2025-06-09T13:18:07Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/CoxConservesAIAgent,https://github.com/cox-cei/CoxConservesAIAgent.git
COXgpt,cox-cei/COXgpt,This repository is to host CoxGpt Python based Solution Accelerator Implementation ,True,Jupyter Notebook,2023-07-13T22:26:29Z,2024-07-21T23:31:34Z,2023-07-27T13:42:08Z,22864,0,0,0,0,main,False,False,https://github.com/cox-cei/COXgpt,https://github.com/cox-cei/COXgpt.git
COXgpt1,cox-cei/COXgpt1,None,True,Jupyter Notebook,2023-07-20T22:47:46Z,2024-10-18T20:05:31Z,2023-09-25T18:33:51Z,6419,0,0,0,0,main,False,False,https://github.com/cox-cei/COXgpt1,https://github.com/cox-cei/COXgpt1.git
CoxGPT1-FastAPI-Backend,cox-cei/CoxGPT1-FastAPI-Backend,None,True,Jupyter Notebook,2023-09-12T18:33:01Z,2024-10-18T20:04:52Z,2024-06-27T16:41:10Z,104379,0,0,0,0,main,False,False,https://github.com/cox-cei/CoxGPT1-FastAPI-Backend,https://github.com/cox-cei/CoxGPT1-FastAPI-Backend.git
CoxGPT1-React-Frontend,cox-cei/CoxGPT1-React-Frontend,None,True,JavaScript,2023-09-12T16:37:22Z,2024-10-18T20:04:44Z,2024-06-27T16:09:34Z,799,0,0,0,0,main,False,False,https://github.com/cox-cei/CoxGPT1-React-Frontend,https://github.com/cox-cei/CoxGPT1-React-Frontend.git
CoxImpact-AIAgent-Test,cox-cei/CoxImpact-AIAgent-Test,,True,,2025-07-02T19:00:04Z,2025-07-23T02:42:47Z,2025-07-23T02:42:44Z,91,0,0,0,0,main,False,False,https://github.com/cox-cei/CoxImpact-AIAgent-Test,https://github.com/cox-cei/CoxImpact-AIAgent-Test.git
CSC-Caller-Verifier,cox-cei/CSC-Caller-Verifier,CSC-Caller-Verifier repository,True,HTML,2023-08-24T18:25:16Z,2025-03-28T00:36:47Z,2025-03-28T00:36:44Z,552,0,0,0,1,main,False,False,https://github.com/cox-cei/CSC-Caller-Verifier,https://github.com/cox-cei/CSC-Caller-Verifier.git
CSC-Kiosk,cox-cei/CSC-Kiosk,Code library for the CSC Live Help Kiosk,True,JavaScript,2024-02-21T19:29:28Z,2024-07-21T03:35:13Z,2024-06-11T19:23:43Z,34755,0,0,0,1,main,False,False,https://github.com/cox-cei/CSC-Kiosk,https://github.com/cox-cei/CSC-Kiosk.git
data-analytics-data-factory,cox-cei/data-analytics-data-factory,None,True,,2023-09-18T14:05:06Z,2025-05-31T13:09:14Z,2025-05-31T13:09:10Z,858,0,0,0,0,main,False,False,https://github.com/cox-cei/data-analytics-data-factory,https://github.com/cox-cei/data-analytics-data-factory.git
data-analytics-sql,cox-cei/data-analytics-sql,None,True,TSQL,2023-09-18T14:05:08Z,2024-10-18T20:06:47Z,2023-10-19T20:08:23Z,58,0,0,0,1,main,False,False,https://github.com/cox-cei/data-analytics-sql,https://github.com/cox-cei/data-analytics-sql.git
data-analytics-tf-deployment,cox-cei/data-analytics-tf-deployment,None,True,HCL,2023-09-21T14:55:49Z,2024-11-07T22:06:31Z,2024-11-07T22:06:27Z,82,0,0,0,1,main,False,False,https://github.com/cox-cei/data-analytics-tf-deployment,https://github.com/cox-cei/data-analytics-tf-deployment.git
de-docker-containerization,cox-cei/de-docker-containerization,docker-containerization,True,Dockerfile,2024-12-26T16:28:13Z,2025-04-15T12:31:42Z,2025-04-15T12:31:38Z,51,0,0,0,0,main,False,False,https://github.com/cox-cei/de-docker-containerization,https://github.com/cox-cei/de-docker-containerization.git
DENVERJAN14,cox-cei/DENVERJAN14,Testing REPO,True,,2025-01-15T15:30:35Z,2025-01-15T15:30:37Z,2025-01-15T15:30:36Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/DENVERJAN14,https://github.com/cox-cei/DENVERJAN14.git
Devops-DORA-Metrics,cox-cei/Devops-DORA-Metrics,Devops DORA Metrics code,True,C#,2025-07-09T15:51:09Z,2025-07-10T21:16:38Z,2025-07-10T21:15:24Z,4097,0,0,0,0,main,False,False,https://github.com/cox-cei/Devops-DORA-Metrics,https://github.com/cox-cei/Devops-DORA-Metrics.git
devops-terraform-sample,cox-cei/devops-terraform-sample,None,True,HCL,2023-08-31T19:59:49Z,2024-10-18T20:07:21Z,2023-09-14T14:46:21Z,61,0,0,0,1,main,False,False,https://github.com/cox-cei/devops-terraform-sample,https://github.com/cox-cei/devops-terraform-sample.git
discoverycenter-webapp-34by34exhibit,cox-cei/discoverycenter-webapp-34by34exhibit,None,True,,2024-01-17T21:25:59Z,2024-10-18T20:07:54Z,2024-01-17T21:25:59Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/discoverycenter-webapp-34by34exhibit,https://github.com/cox-cei/discoverycenter-webapp-34by34exhibit.git
documentation-cybersecurity-policies,cox-cei/documentation-cybersecurity-policies,Cybersecurity policies,True,,2024-02-09T15:43:53Z,2024-07-21T03:00:09Z,2024-02-09T15:43:54Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/documentation-cybersecurity-policies,https://github.com/cox-cei/documentation-cybersecurity-policies.git
dotnet-campustrafficcam-deprecated,cox-cei/dotnet-campustrafficcam-deprecated,None,True,HTML,2024-01-17T21:26:01Z,2024-10-18T20:08:30Z,2024-01-17T21:43:49Z,648,0,0,0,0,master,False,False,https://github.com/cox-cei/dotnet-campustrafficcam-deprecated,https://github.com/cox-cei/dotnet-campustrafficcam-deprecated.git
dotnet-ccigiving-deprecated,cox-cei/dotnet-ccigiving-deprecated,None,True,JavaScript,2024-01-11T21:39:43Z,2024-10-18T20:08:46Z,2024-06-13T16:52:43Z,25485,0,0,0,3,master,False,False,https://github.com/cox-cei/dotnet-ccigiving-deprecated,https://github.com/cox-cei/dotnet-ccigiving-deprecated.git
dotnet-photovettingtool-deprecated,cox-cei/dotnet-photovettingtool-deprecated,None,True,C#,2024-01-17T21:25:57Z,2024-10-18T20:09:04Z,2024-01-17T21:47:58Z,38061,0,0,0,0,master,False,False,https://github.com/cox-cei/dotnet-photovettingtool-deprecated,https://github.com/cox-cei/dotnet-photovettingtool-deprecated.git
dotnet-seasonforsharing-deprecated,cox-cei/dotnet-seasonforsharing-deprecated,None,True,HTML,2024-01-17T21:26:05Z,2024-10-18T20:09:20Z,2024-01-17T21:45:45Z,1967,0,0,0,0,master,False,False,https://github.com/cox-cei/dotnet-seasonforsharing-deprecated,https://github.com/cox-cei/dotnet-seasonforsharing-deprecated.git
EAImpactAssessment-PowerApp,cox-cei/EAImpactAssessment-PowerApp,,True,,2025-04-18T20:39:59Z,2025-06-09T18:46:42Z,2025-06-23T19:06:47Z,5399,1,1,0,1,main,False,False,https://github.com/cox-cei/EAImpactAssessment-PowerApp,https://github.com/cox-cei/EAImpactAssessment-PowerApp.git
EAImpactAssessmentAIAgentTest,cox-cei/EAImpactAssessmentAIAgentTest,,True,,2025-06-20T15:22:03Z,2025-06-29T21:38:24Z,2025-06-29T21:38:22Z,2109,0,0,0,0,main,False,False,https://github.com/cox-cei/EAImpactAssessmentAIAgentTest,https://github.com/cox-cei/EAImpactAssessmentAIAgentTest.git
exampleRepoToImport,cox-cei/exampleRepoToImport,None,True,,2024-10-11T14:35:15Z,2024-10-19T00:33:02Z,2024-10-11T14:35:15Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/exampleRepoToImport,https://github.com/cox-cei/exampleRepoToImport.git
fabric-finops-workspace,cox-cei/fabric-finops-workspace,Finops works,True,Python,2025-04-07T16:39:05Z,2025-06-08T22:50:28Z,2025-06-08T22:50:24Z,437,0,0,0,0,main,False,False,https://github.com/cox-cei/fabric-finops-workspace,https://github.com/cox-cei/fabric-finops-workspace.git
Flexdeploy_Java_Fix,cox-cei/Flexdeploy_Java_Fix,The Pipeline in this repo fix java used in Flexdeploy after patching Java,True,Shell,2025-05-20T20:10:32Z,2025-06-21T02:43:32Z,2025-06-21T02:43:29Z,32,0,0,0,0,main,False,False,https://github.com/cox-cei/Flexdeploy_Java_Fix,https://github.com/cox-cei/Flexdeploy_Java_Fix.git
FLORIDAGITHUBREPO,cox-cei/FLORIDAGITHUBREPO,FLORIDAGITHUBREPO,True,,2024-12-18T18:45:28Z,2024-12-18T18:45:30Z,2024-12-18T18:45:29Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/FLORIDAGITHUBREPO,https://github.com/cox-cei/FLORIDAGITHUBREPO.git
FloridaTEAM,cox-cei/FloridaTEAM,FLORIDA,True,,2024-12-17T16:03:58Z,2024-12-17T16:04:00Z,2024-12-17T16:03:58Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/FloridaTEAM,https://github.com/cox-cei/FloridaTEAM.git
functionapp-prompty,cox-cei/functionapp-prompty,This is going’s to be a repo to store Prompty either .prompty or .txt files for AIFacotry,True,,2025-01-17T15:16:17Z,2025-01-23T13:17:33Z,2025-01-23T13:17:30Z,7,0,0,0,0,main,False,False,https://github.com/cox-cei/functionapp-prompty,https://github.com/cox-cei/functionapp-prompty.git
Github-Governance,cox-cei/Github-Governance,📋 Cox CEI GitHub Standards & Best Practices,True,,2025-07-02T15:32:50Z,2025-07-02T17:52:48Z,2025-07-02T17:52:44Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/Github-Governance,https://github.com/cox-cei/Github-Governance.git
github-repository-governance,cox-cei/github-repository-governance,Self service access to create github repositories and teams,True,HCL,2023-08-09T20:29:39Z,2024-10-18T20:09:45Z,2024-12-04T16:15:43Z,803,0,0,0,2,main,False,False,https://github.com/cox-cei/github-repository-governance,https://github.com/cox-cei/github-repository-governance.git
github-reusable-workflows,cox-cei/github-reusable-workflows,"GitHub Reusable Workflows for all Teams. Terraform, CI/CD Automation",True,,2023-08-18T12:41:47Z,2025-07-16T12:27:05Z,2025-07-16T12:27:01Z,89,0,0,0,0,main,False,False,https://github.com/cox-cei/github-reusable-workflows,https://github.com/cox-cei/github-reusable-workflows.git
github-template-automated-release,cox-cei/github-template-automated-release,Automated repository release template for GitHub,True,,2023-08-14T13:17:34Z,2023-08-17T20:03:12Z,2023-08-17T15:38:27Z,31,0,0,0,0,main,False,False,https://github.com/cox-cei/github-template-automated-release,https://github.com/cox-cei/github-template-automated-release.git
github-terraform-governance,cox-cei/github-terraform-governance,None,True,HCL,2024-07-18T06:43:12Z,2024-10-18T22:52:50Z,2024-07-31T15:24:26Z,587,0,0,0,0,main,False,False,https://github.com/cox-cei/github-terraform-governance,https://github.com/cox-cei/github-terraform-governance.git
Github-TerraformCloud-Governance,cox-cei/Github-TerraformCloud-Governance,Hosts the Terraform code for governing GitHub via Terraform Cloud,True,HCL,2024-09-03T18:38:29Z,2025-08-01T13:43:31Z,2025-08-01T13:43:28Z,1457,0,0,0,1,main,False,False,https://github.com/cox-cei/Github-TerraformCloud-Governance,https://github.com/cox-cei/Github-TerraformCloud-Governance.git
IA-APSharedServices-Dispatcher,cox-cei/IA-APSharedServices-Dispatcher,The dispatcher part of the bot that will load emails from the AP Shared Services Shared Mailbox into a queue,True,,2024-06-21T15:08:34Z,2025-06-26T14:10:31Z,2025-06-26T14:10:32Z,8849,0,0,0,0,main,False,False,https://github.com/cox-cei/IA-APSharedServices-Dispatcher,https://github.com/cox-cei/IA-APSharedServices-Dispatcher.git
IA-APSharedServices-Performer,cox-cei/IA-APSharedServices-Performer,The performer part of the bot will take action on emails that were uploaded into the queue by the dispatcher,True,HTML,2024-06-21T15:08:32Z,2025-07-18T08:44:46Z,2025-07-18T08:44:43Z,8066,0,0,0,0,main,False,False,https://github.com/cox-cei/IA-APSharedServices-Performer,https://github.com/cox-cei/IA-APSharedServices-Performer.git
IA-SharePointContentTagging-Dispatcher,cox-cei/IA-SharePointContentTagging-Dispatcher,A UiPath Dispatcher that traverses current content on a SharePoint site to then later tag and summarize that piece of content.,True,HTML,2024-06-17T13:33:49Z,2024-08-02T19:05:47Z,2024-11-14T21:48:32Z,3688,0,0,0,0,main,False,False,https://github.com/cox-cei/IA-SharePointContentTagging-Dispatcher,https://github.com/cox-cei/IA-SharePointContentTagging-Dispatcher.git
IA-SharePointContentTagging-Performer,cox-cei/IA-SharePointContentTagging-Performer,A UiPath Performer that works the content items in the queue to tag and summarize that piece of content. Then the automation will upload that newly formed metadata into SharePoint to make the content more searchable.,True,C#,2024-06-21T14:37:46Z,2024-11-14T21:45:13Z,2024-11-14T21:45:31Z,95773,0,0,0,0,main,False,False,https://github.com/cox-cei/IA-SharePointContentTagging-Performer,https://github.com/cox-cei/IA-SharePointContentTagging-Performer.git
idn-sailpoint,cox-cei/idn-sailpoint,This repository will store any custom code used to support the SailPoint Identity Management solution,True,Java,2025-05-07T18:42:44Z,2025-07-21T14:07:50Z,2025-07-21T14:07:46Z,147,0,0,0,0,main,False,False,https://github.com/cox-cei/idn-sailpoint,https://github.com/cox-cei/idn-sailpoint.git
impactful,cox-cei/impactful,None,True,,2023-05-25T13:47:32Z,2024-10-18T20:10:27Z,2023-05-25T13:47:33Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/impactful,https://github.com/cox-cei/impactful.git
informatica-cloud,cox-cei/informatica-cloud,,True,Python,2024-10-25T14:59:33Z,2024-11-01T15:33:16Z,2024-11-04T02:39:58Z,5522,0,0,0,0,prod,False,False,https://github.com/cox-cei/informatica-cloud,https://github.com/cox-cei/informatica-cloud.git
informatica-cloud-dap,cox-cei/informatica-cloud-dap,CEI Informatica Assets and DevOps Pipeline Repository.,True,Python,2023-06-02T16:44:59Z,2024-11-04T02:53:41Z,2025-07-25T16:52:01Z,5737,0,0,0,0,prod,False,False,https://github.com/cox-cei/informatica-cloud-dap,https://github.com/cox-cei/informatica-cloud-dap.git
informatica-hcm-pipeline,cox-cei/informatica-hcm-pipeline,Repo to Host HCM Informatica Assets,True,Python,2025-06-18T17:23:37Z,2025-07-29T18:25:28Z,2025-08-01T05:27:03Z,5742,0,0,0,0,qa,False,False,https://github.com/cox-cei/informatica-hcm-pipeline,https://github.com/cox-cei/informatica-hcm-pipeline.git
InfoSecEng-general-repo,cox-cei/InfoSecEng-general-repo,InfoSecEngineering General code repo,True,PowerShell,2025-07-16T14:26:39Z,2025-07-16T14:39:24Z,2025-07-16T14:39:21Z,87,0,0,0,0,main,False,False,https://github.com/cox-cei/InfoSecEng-general-repo,https://github.com/cox-cei/InfoSecEng-general-repo.git
Innovation,cox-cei/Innovation,,True,,2025-07-03T15:46:51Z,2025-07-03T15:46:53Z,2025-07-03T15:46:52Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/Innovation,https://github.com/cox-cei/Innovation.git
InsideCox-Portal-Assistant,cox-cei/InsideCox-Portal-Assistant,Inside Cox Portal Assistant,True,,2025-07-23T17:14:52Z,2025-07-23T21:23:15Z,2025-07-23T21:23:13Z,82,0,0,0,0,main,False,False,https://github.com/cox-cei/InsideCox-Portal-Assistant,https://github.com/cox-cei/InsideCox-Portal-Assistant.git
InsideCox-SetUp-Demo,cox-cei/InsideCox-SetUp-Demo,None,True,TypeScript,2024-10-16T18:27:43Z,2024-11-04T18:03:18Z,2024-11-05T17:01:03Z,37443,0,0,0,3,prudhvi,False,False,https://github.com/cox-cei/InsideCox-SetUp-Demo,https://github.com/cox-cei/InsideCox-SetUp-Demo.git
internal-oracle-webcenter,cox-cei/internal-oracle-webcenter,Generic activities for downloading Oracle app from web and interact with the application,True,C#,2025-04-02T14:54:14Z,2025-07-14T20:52:49Z,2025-07-25T16:17:20Z,34970,0,0,0,2,main,False,False,https://github.com/cox-cei/internal-oracle-webcenter,https://github.com/cox-cei/internal-oracle-webcenter.git
Internal-Oracle-Webcenter-Library,cox-cei/Internal-Oracle-Webcenter-Library,,True,,2024-12-05T14:57:58Z,2024-12-05T14:58:00Z,2024-12-05T14:57:58Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/Internal-Oracle-Webcenter-Library,https://github.com/cox-cei/Internal-Oracle-Webcenter-Library.git
itopsdatalakehouse-data-factory,cox-cei/itopsdatalakehouse-data-factory,ITOps Repo for Data Factory and deployment pipeline,True,,2024-10-07T18:20:53Z,2025-06-18T14:49:29Z,2025-06-18T14:49:26Z,380,0,0,0,0,main,False,False,https://github.com/cox-cei/itopsdatalakehouse-data-factory,https://github.com/cox-cei/itopsdatalakehouse-data-factory.git
itopsdatalakehouse-fabric-workspace,cox-cei/itopsdatalakehouse-fabric-workspace,IT Ops Repo for Fabric workspaces,True,,2025-01-15T16:35:47Z,2025-01-17T17:06:40Z,2025-07-31T18:25:14Z,202,0,0,0,0,main,False,False,https://github.com/cox-cei/itopsdatalakehouse-fabric-workspace,https://github.com/cox-cei/itopsdatalakehouse-fabric-workspace.git
itopsdatalakehouse-meta-database,cox-cei/itopsdatalakehouse-meta-database,ITOps Repo for the meta database project and deployment pipeline,True,TSQL,2024-10-07T18:20:51Z,2025-07-28T16:47:17Z,2025-07-28T16:47:15Z,179,0,0,0,0,main,False,False,https://github.com/cox-cei/itopsdatalakehouse-meta-database,https://github.com/cox-cei/itopsdatalakehouse-meta-database.git
itopsdatalakehouse-tf-deployment,cox-cei/itopsdatalakehouse-tf-deployment,ITOps Repo for terraform deployment of Azure infrastructure as code,True,HCL,2024-10-07T18:20:50Z,2025-07-28T18:29:11Z,2025-07-28T18:29:08Z,55,0,0,0,0,main,False,False,https://github.com/cox-cei/itopsdatalakehouse-tf-deployment,https://github.com/cox-cei/itopsdatalakehouse-tf-deployment.git
lawpolicy-fabric-dc-workspace,cox-cei/lawpolicy-fabric-dc-workspace,For Azure Fabric Law & Policy  DC ( Data Consumers) workspaces,True,,2025-07-01T18:18:28Z,2025-07-11T17:00:57Z,2025-07-02T13:46:21Z,1,0,0,0,0,main,False,False,https://github.com/cox-cei/lawpolicy-fabric-dc-workspace,https://github.com/cox-cei/lawpolicy-fabric-dc-workspace.git
lawpolicylakehouse-data-factory,cox-cei/lawpolicylakehouse-data-factory,For Azure Fabric Law & Policy  Azure Data Factories (ADF's) ,True,,2025-06-30T20:11:33Z,2025-07-07T18:29:43Z,2025-07-07T18:29:41Z,10,0,0,0,0,main,False,False,https://github.com/cox-cei/lawpolicylakehouse-data-factory,https://github.com/cox-cei/lawpolicylakehouse-data-factory.git
lawpolicylakehouse-fabric-workspace,cox-cei/lawpolicylakehouse-fabric-workspace,For Azure Fabric Law & Policy  DC ( Data Producers) workspaces,True,,2025-06-30T20:12:23Z,2025-07-11T19:14:56Z,2025-07-11T19:14:51Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/lawpolicylakehouse-fabric-workspace,https://github.com/cox-cei/lawpolicylakehouse-fabric-workspace.git
lawpolicylakehouse-meta-database,cox-cei/lawpolicylakehouse-meta-database,For Azure Fabric Law & Policy  Terraform metadata repository ,True,TSQL,2025-06-30T20:11:09Z,2025-07-07T19:19:09Z,2025-07-16T15:50:43Z,3157,0,0,0,0,main,False,False,https://github.com/cox-cei/lawpolicylakehouse-meta-database,https://github.com/cox-cei/lawpolicylakehouse-meta-database.git
lawpolicylakehouse-tf-deployment,cox-cei/lawpolicylakehouse-tf-deployment,Terraform Infrastructure As a Code Repository ,True,HCL,2025-06-26T17:39:07Z,2025-07-02T20:04:09Z,2025-07-02T20:04:07Z,20,0,0,0,0,main,False,False,https://github.com/cox-cei/lawpolicylakehouse-tf-deployment,https://github.com/cox-cei/lawpolicylakehouse-tf-deployment.git
LCM-Dashboard-MVP,cox-cei/LCM-Dashboard-MVP,MVP for the LCM,True,Python,2025-04-30T06:33:29Z,2025-07-24T12:41:31Z,2025-07-24T12:41:27Z,91,1,1,0,85,main,False,False,https://github.com/cox-cei/LCM-Dashboard-MVP,https://github.com/cox-cei/LCM-Dashboard-MVP.git
mediaservers-emailsignaturegenerator,cox-cei/mediaservers-emailsignaturegenerator,None,True,HTML,2024-01-17T21:25:53Z,2024-10-18T20:11:12Z,2024-01-17T21:35:03Z,2046,0,0,0,0,master,False,False,https://github.com/cox-cei/mediaservers-emailsignaturegenerator,https://github.com/cox-cei/mediaservers-emailsignaturegenerator.git
MicrosoftGraph-API-Library,cox-cei/MicrosoftGraph-API-Library,None,True,,2024-09-26T21:20:14Z,2025-02-14T18:17:15Z,2025-02-14T18:23:54Z,396,0,0,0,0,main,False,False,https://github.com/cox-cei/MicrosoftGraph-API-Library,https://github.com/cox-cei/MicrosoftGraph-API-Library.git
msfabric-core-engg,cox-cei/msfabric-core-engg,MS Fabric Lakehouse Core/Platform team,True,,2024-08-05T15:40:09Z,2024-08-05T15:40:14Z,2024-08-05T15:40:10Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/msfabric-core-engg,https://github.com/cox-cei/msfabric-core-engg.git
MSfabric-DataEng-CoreLibs,cox-cei/MSfabric-DataEng-CoreLibs,Data Engineering/Pipelines Infrastructure & Core Libraries,True,,2024-08-05T15:40:07Z,2024-08-05T15:40:12Z,2024-08-05T15:40:08Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/MSfabric-DataEng-CoreLibs,https://github.com/cox-cei/MSfabric-DataEng-CoreLibs.git
NeoLoad-PerformanceTesting,cox-cei/NeoLoad-PerformanceTesting,Backup QA NeoLoad Performance Testing Scripts,True,,2024-08-29T14:23:30Z,2024-08-29T14:23:38Z,2024-08-29T14:23:30Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/NeoLoad-PerformanceTesting,https://github.com/cox-cei/NeoLoad-PerformanceTesting.git
NEWMEXICOTESTINGTEAM,cox-cei/NEWMEXICOTESTINGTEAM,TESTING TEAM,True,,2024-12-18T18:37:31Z,2024-12-18T18:37:32Z,2024-12-18T18:37:31Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/NEWMEXICOTESTINGTEAM,https://github.com/cox-cei/NEWMEXICOTESTINGTEAM.git
observability-scripts,cox-cei/observability-scripts,Main working repo for new script development before hand off,True,,2025-01-28T14:53:55Z,2025-02-26T18:00:45Z,2025-02-26T18:00:42Z,18,1,1,0,0,main,False,False,https://github.com/cox-cei/observability-scripts,https://github.com/cox-cei/observability-scripts.git
OCI-ansible-Automation-Inventory,cox-cei/OCI-ansible-Automation-Inventory,OCI DBA repository,True,,2025-01-27T16:47:40Z,2025-01-27T16:47:42Z,2025-01-27T16:47:40Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/OCI-ansible-Automation-Inventory,https://github.com/cox-cei/OCI-ansible-Automation-Inventory.git
OCI-API-Neoload-PerformanceTesting,cox-cei/OCI-API-Neoload-PerformanceTesting,,True,JavaScript,2024-11-18T21:25:46Z,2025-01-30T20:51:05Z,2025-01-30T20:50:59Z,2491,0,0,0,0,master,False,False,https://github.com/cox-cei/OCI-API-Neoload-PerformanceTesting,https://github.com/cox-cei/OCI-API-Neoload-PerformanceTesting.git
OCI-OracleEBS-Datafix,cox-cei/OCI-OracleEBS-Datafix,Oracle EBS repo for CEI,True,PLSQL,2024-04-15T03:11:54Z,2025-08-02T13:02:40Z,2025-08-02T13:02:38Z,2599,0,0,0,39,develop,False,False,https://github.com/cox-cei/OCI-OracleEBS-Datafix,https://github.com/cox-cei/OCI-OracleEBS-Datafix.git
OCI-OracleEBS-XXCCI,cox-cei/OCI-OracleEBS-XXCCI,Oracle EBS repo for CCI,True,Rich Text Format,2024-03-11T18:56:18Z,2025-08-01T21:44:27Z,2025-08-01T21:44:24Z,56842,1,1,0,6,develop,False,False,https://github.com/cox-cei/OCI-OracleEBS-XXCCI,https://github.com/cox-cei/OCI-OracleEBS-XXCCI.git
OCI-OracleEBS-XXCCI2,cox-cei/OCI-OracleEBS-XXCCI2,None,True,Rich Text Format,2024-04-12T14:43:50Z,2024-10-18T22:55:11Z,2024-04-12T19:56:11Z,31874,0,0,0,0,master,False,False,https://github.com/cox-cei/OCI-OracleEBS-XXCCI2,https://github.com/cox-cei/OCI-OracleEBS-XXCCI2.git
OCI-OracleEBS-xxce2,cox-cei/OCI-OracleEBS-xxce2,GITHUB REPOSITORY,True,,2024-12-17T15:48:54Z,2024-12-17T15:48:56Z,2024-12-17T15:48:54Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/OCI-OracleEBS-xxce2,https://github.com/cox-cei/OCI-OracleEBS-xxce2.git
OCI-OracleEBS-XXCEI,cox-cei/OCI-OracleEBS-XXCEI,Oracle EBS repo for CEI,True,Rich Text Format,2024-03-07T14:43:40Z,2025-08-03T18:25:18Z,2025-08-03T18:25:15Z,217308,1,1,0,2,develop,False,False,https://github.com/cox-cei/OCI-OracleEBS-XXCEI,https://github.com/cox-cei/OCI-OracleEBS-XXCEI.git
OCI-OracleEBS-XXCMG,cox-cei/OCI-OracleEBS-XXCMG,Oracle EBS repo for CMG,True,Rich Text Format,2024-03-11T18:56:10Z,2025-04-25T15:36:47Z,2025-05-09T16:28:45Z,46328,1,1,0,0,develop,False,False,https://github.com/cox-cei/OCI-OracleEBS-XXCMG,https://github.com/cox-cei/OCI-OracleEBS-XXCMG.git
OCI-Webcenter-CCI-SOA,cox-cei/OCI-Webcenter-CCI-SOA,Oracle EBS repo for CMG,True,XSLT,2024-03-12T14:50:02Z,2024-07-21T02:43:13Z,2025-03-07T16:30:03Z,1180,0,0,0,0,master,False,False,https://github.com/cox-cei/OCI-Webcenter-CCI-SOA,https://github.com/cox-cei/OCI-Webcenter-CCI-SOA.git
OCI-Webcenter-CEI-SOA,cox-cei/OCI-Webcenter-CEI-SOA,Oracle EBS repo for CMG,True,XSLT,2024-03-12T14:50:00Z,2024-07-21T03:23:26Z,2024-04-30T13:12:14Z,960,0,0,0,0,master,False,False,https://github.com/cox-cei/OCI-Webcenter-CEI-SOA,https://github.com/cox-cei/OCI-Webcenter-CEI-SOA.git
OCI-Webcenter-CMG-SOA,cox-cei/OCI-Webcenter-CMG-SOA,Oracle EBS repo for CMG,True,XSLT,2024-03-12T14:50:04Z,2024-07-21T02:42:56Z,2024-04-30T13:12:32Z,973,0,0,0,0,master,False,False,https://github.com/cox-cei/OCI-Webcenter-CMG-SOA,https://github.com/cox-cei/OCI-Webcenter-CMG-SOA.git
OCI-Webcenter-nonSOA-AllOrgs,cox-cei/OCI-Webcenter-nonSOA-AllOrgs,Oracle EBS repo for CMG,True,,2024-03-12T14:49:58Z,2025-07-29T19:18:03Z,2025-08-04T03:04:57Z,387,0,0,0,0,main,False,False,https://github.com/cox-cei/OCI-Webcenter-nonSOA-AllOrgs,https://github.com/cox-cei/OCI-Webcenter-nonSOA-AllOrgs.git
oic-deployment-actions,cox-cei/oic-deployment-actions,Automated deployment workflows for OIC using GitHub Actions,True,Shell,2024-12-04T20:47:53Z,2025-07-14T11:36:04Z,2025-07-14T11:36:02Z,352,0,0,0,0,gen3,False,False,https://github.com/cox-cei/oic-deployment-actions,https://github.com/cox-cei/oic-deployment-actions.git
onprem-dotnet-cautoincentivesadmin,cox-cei/onprem-dotnet-cautoincentivesadmin,None,True,JavaScript,2024-03-04T21:15:53Z,2024-10-18T20:13:45Z,2024-03-04T21:56:08Z,14154,0,0,0,0,main,False,False,https://github.com/cox-cei/onprem-dotnet-cautoincentivesadmin,https://github.com/cox-cei/onprem-dotnet-cautoincentivesadmin.git
onprem-dotnet-ccitreasury,cox-cei/onprem-dotnet-ccitreasury,None,True,ASP.NET,2024-03-04T21:16:31Z,2024-10-18T20:13:54Z,2024-03-04T22:04:03Z,1893,0,0,0,0,main,False,False,https://github.com/cox-cei/onprem-dotnet-ccitreasury,https://github.com/cox-cei/onprem-dotnet-ccitreasury.git
onprem-dotnet-ceitreasury,cox-cei/onprem-dotnet-ceitreasury,None,True,ASP.NET,2024-03-04T21:15:48Z,2024-10-18T20:14:22Z,2024-03-04T21:53:37Z,983,0,0,0,0,main,False,False,https://github.com/cox-cei/onprem-dotnet-ceitreasury,https://github.com/cox-cei/onprem-dotnet-ceitreasury.git
onprem-dotnet-cerfarchive,cox-cei/onprem-dotnet-cerfarchive,None,True,HTML,2024-03-04T21:16:04Z,2024-10-18T20:14:37Z,2024-03-04T22:03:26Z,4784,0,0,0,0,main,False,False,https://github.com/cox-cei/onprem-dotnet-cerfarchive,https://github.com/cox-cei/onprem-dotnet-cerfarchive.git
onprem-dotnet-cerfqaarchive,cox-cei/onprem-dotnet-cerfqaarchive,None,True,HTML,2024-03-04T21:15:55Z,2024-10-18T20:15:00Z,2024-03-04T21:57:18Z,4785,0,0,0,0,main,False,False,https://github.com/cox-cei/onprem-dotnet-cerfqaarchive,https://github.com/cox-cei/onprem-dotnet-cerfqaarchive.git
onprem-dotnet-mgtdirectory,cox-cei/onprem-dotnet-mgtdirectory,None,True,Classic ASP,2024-03-04T21:16:00Z,2024-10-18T20:15:15Z,2024-03-04T22:01:44Z,897,0,0,0,0,main,False,False,https://github.com/cox-cei/onprem-dotnet-mgtdirectory,https://github.com/cox-cei/onprem-dotnet-mgtdirectory.git
onprem-psscripts-operations,cox-cei/onprem-psscripts-operations,None,True,PowerShell,2024-03-04T21:15:57Z,2024-10-18T20:15:35Z,2024-03-04T21:58:11Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/onprem-psscripts-operations,https://github.com/cox-cei/onprem-psscripts-operations.git
Oracle-Automation-Scripts,cox-cei/Oracle-Automation-Scripts,Scripts and Jobs for the Oracle Automation,True,,2025-06-19T14:21:09Z,2025-07-08T13:07:41Z,2025-07-08T13:07:38Z,57,0,0,0,0,main,False,False,https://github.com/cox-cei/Oracle-Automation-Scripts,https://github.com/cox-cei/Oracle-Automation-Scripts.git
Oracle-MTP-Automation-Scripts,cox-cei/Oracle-MTP-Automation-Scripts,Automates the creation of MTP documents when releases are migrated to stage in FlexDeploy,True,Python,2025-06-19T18:51:10Z,2025-07-18T17:55:15Z,2025-07-31T18:45:05Z,775,0,0,0,0,main,False,False,https://github.com/cox-cei/Oracle-MTP-Automation-Scripts,https://github.com/cox-cei/Oracle-MTP-Automation-Scripts.git
Oracle-Neoload-PerformanceTesting,cox-cei/Oracle-Neoload-PerformanceTesting,,True,JavaScript,2024-11-18T19:06:24Z,2024-11-22T22:42:07Z,2025-05-05T14:51:59Z,11965,0,0,0,0,master,False,False,https://github.com/cox-cei/Oracle-Neoload-PerformanceTesting,https://github.com/cox-cei/Oracle-Neoload-PerformanceTesting.git
oracle-observability-scripts,cox-cei/oracle-observability-scripts,SRE scripts,True,JavaScript,2025-01-15T16:41:47Z,2025-04-30T18:07:42Z,2025-04-30T18:07:38Z,2147,0,0,0,0,main,False,False,https://github.com/cox-cei/oracle-observability-scripts,https://github.com/cox-cei/oracle-observability-scripts.git
oracle-ods-edw-scripts,cox-cei/oracle-ods-edw-scripts,Scripts for ODS (Oracle Operational Data Store) and EDW (Enterprise Data Warehouse).,True,,2024-03-06T20:33:13Z,2024-07-21T02:22:14Z,2024-03-08T03:08:53Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/oracle-ods-edw-scripts,https://github.com/cox-cei/oracle-ods-edw-scripts.git
Oracle-SupplierDoc-Upload-Scripts,cox-cei/Oracle-SupplierDoc-Upload-Scripts,"Scripts that download, rename, and upload supplier documents.",True,Python,2025-07-08T13:58:43Z,2025-07-15T15:18:00Z,2025-07-15T15:17:57Z,757,0,0,0,0,main,False,False,https://github.com/cox-cei/Oracle-SupplierDoc-Upload-Scripts,https://github.com/cox-cei/Oracle-SupplierDoc-Upload-Scripts.git
Oracle-XXCEI-Test,cox-cei/Oracle-XXCEI-Test,Test the github actions on release,True,Python,2024-03-26T21:01:38Z,2025-07-16T12:22:34Z,2025-07-16T12:22:31Z,313,0,0,0,2,dev,False,False,https://github.com/cox-cei/Oracle-XXCEI-Test,https://github.com/cox-cei/Oracle-XXCEI-Test.git
PA-CEI-Finance-Tax-Diligent-Company-Profile-Performer,cox-cei/PA-CEI-Finance-Tax-Diligent-Company-Profile-Performer,,True,,2025-07-18T12:26:45Z,2025-07-29T16:30:39Z,2025-07-29T16:30:43Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/PA-CEI-Finance-Tax-Diligent-Company-Profile-Performer,https://github.com/cox-cei/PA-CEI-Finance-Tax-Diligent-Company-Profile-Performer.git
packer-images,cox-cei/packer-images,None,True,HCL,2023-11-09T16:36:17Z,2025-07-30T00:17:07Z,2025-07-30T00:17:03Z,650,0,0,0,0,main,False,False,https://github.com/cox-cei/packer-images,https://github.com/cox-cei/packer-images.git
PlatformAutomation-general-repo,cox-cei/PlatformAutomation-general-repo,General Code repository,True,PowerShell,2024-07-11T14:17:14Z,2025-05-30T15:37:20Z,2025-06-10T13:47:36Z,47432,1,1,0,0,main,False,False,https://github.com/cox-cei/PlatformAutomation-general-repo,https://github.com/cox-cei/PlatformAutomation-general-repo.git
platformAutomation_coxEnterpriseCloudVRO,cox-cei/platformAutomation_coxEnterpriseCloudVRO,None,True,,2023-07-06T17:59:41Z,2024-10-18T20:17:14Z,2023-07-06T19:28:41Z,672,0,0,0,0,main,False,False,https://github.com/cox-cei/platformAutomation_coxEnterpriseCloudVRO,https://github.com/cox-cei/platformAutomation_coxEnterpriseCloudVRO.git
platformAutomation_ome,cox-cei/platformAutomation_ome,None,True,,2023-07-06T17:59:42Z,2024-10-18T20:17:29Z,2023-07-06T19:29:44Z,108,0,0,0,0,main,False,False,https://github.com/cox-cei/platformAutomation_ome,https://github.com/cox-cei/platformAutomation_ome.git
platformAutomation_omepy,cox-cei/platformAutomation_omepy,None,True,Python,2023-07-06T17:59:43Z,2024-10-18T20:17:47Z,2023-07-06T19:31:14Z,15,0,0,0,0,main,False,False,https://github.com/cox-cei/platformAutomation_omepy,https://github.com/cox-cei/platformAutomation_omepy.git
poc-hcm-glui,cox-cei/poc-hcm-glui,,True,HTML,2024-11-21T04:13:42Z,2024-12-20T22:11:33Z,2024-12-20T22:11:29Z,494,0,0,0,0,main,False,False,https://github.com/cox-cei/poc-hcm-glui,https://github.com/cox-cei/poc-hcm-glui.git
poc_hcm,cox-cei/poc_hcm,None,True,C#,2024-09-24T19:59:28Z,2024-12-05T06:04:13Z,2024-12-05T06:04:10Z,334,0,0,0,0,main,False,False,https://github.com/cox-cei/poc_hcm,https://github.com/cox-cei/poc_hcm.git
poc_oic,cox-cei/poc_oic,None,True,Shell,2024-10-07T16:07:16Z,2024-12-05T19:52:24Z,2024-12-26T21:47:50Z,402,0,0,0,1,main,False,False,https://github.com/cox-cei/poc_oic,https://github.com/cox-cei/poc_oic.git
powerplatform-CITA,cox-cei/powerplatform-CITA,None,True,,2024-06-10T18:25:59Z,2025-07-23T23:22:49Z,2025-07-23T23:22:45Z,32240,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-CITA,https://github.com/cox-cei/powerplatform-CITA.git
powerplatform-dev-peoplemanagementtool,cox-cei/powerplatform-dev-peoplemanagementtool,People Management Tool,True,,2024-04-01T17:26:58Z,2025-03-20T11:05:07Z,2025-08-01T14:39:54Z,33683,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-dev-peoplemanagementtool,https://github.com/cox-cei/powerplatform-dev-peoplemanagementtool.git
powerplatform-powerapps--BiWeeklyStatusApplication,cox-cei/powerplatform-powerapps--BiWeeklyStatusApplication,Bi-Weekly Status Application,True,,2024-05-06T15:23:08Z,2025-07-25T08:44:21Z,2025-08-01T14:39:40Z,10942,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerapps--BiWeeklyStatusApplication,https://github.com/cox-cei/powerplatform-powerapps--BiWeeklyStatusApplication.git
powerplatform-powerapps-AccomplishmentsList,cox-cei/powerplatform-powerapps-AccomplishmentsList,Accomplishments List,True,,2025-04-07T15:30:11Z,2025-07-18T20:24:18Z,2025-08-01T14:39:50Z,134,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerapps-AccomplishmentsList,https://github.com/cox-cei/powerplatform-powerapps-AccomplishmentsList.git
powerplatform-powerapps-ApplicationEventLoggingSystem,cox-cei/powerplatform-powerapps-ApplicationEventLoggingSystem,Application Event Logging System,True,,2025-03-24T13:55:04Z,2025-07-28T13:54:24Z,2025-08-01T14:40:58Z,8552,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerapps-ApplicationEventLoggingSystem,https://github.com/cox-cei/powerplatform-powerapps-ApplicationEventLoggingSystem.git
powerplatform-powerapps-audiencetargetingdiscovery,cox-cei/powerplatform-powerapps-audiencetargetingdiscovery,Audience Targeting Discovery Tool,True,,2024-08-09T14:42:08Z,2025-07-28T13:57:34Z,2025-08-01T14:40:32Z,854,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerapps-audiencetargetingdiscovery,https://github.com/cox-cei/powerplatform-powerapps-audiencetargetingdiscovery.git
powerplatform-powerapps-bcp-enablement,cox-cei/powerplatform-powerapps-bcp-enablement,BCP Enablement,True,,2024-12-17T14:16:21Z,2025-07-28T13:58:47Z,2025-08-01T14:38:55Z,96778,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerapps-bcp-enablement,https://github.com/cox-cei/powerplatform-powerapps-bcp-enablement.git
powerplatform-powerapps-bcpsurveysolution,cox-cei/powerplatform-powerapps-bcpsurveysolution, BCP Survey Solution,True,,2024-03-06T19:54:04Z,2025-07-28T13:55:58Z,2025-08-01T14:39:43Z,4410,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerapps-bcpsurveysolution,https://github.com/cox-cei/powerplatform-powerapps-bcpsurveysolution.git
powerplatform-powerapps-cci-bcp,cox-cei/powerplatform-powerapps-cci-bcp,CCI BCP Team Maintenance and Tracking,True,,2024-03-06T20:16:06Z,2025-08-01T11:12:23Z,2025-08-01T14:40:32Z,590,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerapps-cci-bcp,https://github.com/cox-cei/powerplatform-powerapps-cci-bcp.git
powerplatform-powerapps-coxonemodern-archive,cox-cei/powerplatform-powerapps-coxonemodern-archive,CoxOneModern-Archive,True,,2024-03-26T17:48:46Z,2025-07-16T15:44:39Z,2025-08-01T14:40:38Z,3441,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerapps-coxonemodern-archive,https://github.com/cox-cei/powerplatform-powerapps-coxonemodern-archive.git
powerplatform-powerapps-internationalbusinessrisk,cox-cei/powerplatform-powerapps-internationalbusinessrisk,International Business Risk,True,,2024-02-19T18:33:46Z,2025-06-18T11:52:52Z,2025-08-01T14:40:46Z,6776,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerapps-internationalbusinessrisk,https://github.com/cox-cei/powerplatform-powerapps-internationalbusinessrisk.git
powerplatform-powerapps-isightuet,cox-cei/powerplatform-powerapps-isightuet,iSight UET,True,,2024-03-06T19:54:07Z,2025-06-24T14:37:40Z,2025-08-01T14:41:21Z,4310,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerapps-isightuet,https://github.com/cox-cei/powerplatform-powerapps-isightuet.git
powerplatform-powerapps-ITTeamInventory,cox-cei/powerplatform-powerapps-ITTeamInventory,IT Team Inventory,True,,2024-05-10T12:27:56Z,2025-07-02T12:42:05Z,2025-08-01T14:38:55Z,4511,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerapps-ITTeamInventory,https://github.com/cox-cei/powerplatform-powerapps-ITTeamInventory.git
powerplatform-powerapps-MoveSitePages,cox-cei/powerplatform-powerapps-MoveSitePages,Corpcom-Move Site Pages,True,,2025-07-28T12:49:58Z,2025-07-29T13:40:30Z,2025-07-31T14:39:56Z,2090,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerapps-MoveSitePages,https://github.com/cox-cei/powerplatform-powerapps-MoveSitePages.git
powerplatform-powerapps-profilechecker,cox-cei/powerplatform-powerapps-profilechecker,Profile Checker,True,,2024-05-16T15:43:42Z,2025-08-01T13:13:23Z,2025-08-01T14:40:04Z,10984,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerapps-profilechecker,https://github.com/cox-cei/powerplatform-powerapps-profilechecker.git
powerplatform-powerapps-travelrisk,cox-cei/powerplatform-powerapps-travelrisk,Travel Risk,True,HTML,2024-06-11T14:20:22Z,2025-07-01T14:10:39Z,2025-08-01T14:43:21Z,26784,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerapps-travelrisk,https://github.com/cox-cei/powerplatform-powerapps-travelrisk.git
powerplatform-powerapps-VendorComplianceAudit,cox-cei/powerplatform-powerapps-VendorComplianceAudit,Vendor Compliance Audit,True,,2024-04-17T14:03:43Z,2025-05-28T10:23:52Z,2025-08-01T14:41:26Z,7904,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerapps-VendorComplianceAudit,https://github.com/cox-cei/powerplatform-powerapps-VendorComplianceAudit.git
powerplatform-powerpages-ccigiving,cox-cei/powerplatform-powerpages-ccigiving,None,True,JavaScript,2024-01-09T20:41:42Z,2025-07-08T02:51:29Z,2025-08-01T14:43:02Z,49126,0,0,0,0,main,False,False,https://github.com/cox-cei/powerplatform-powerpages-ccigiving,https://github.com/cox-cei/powerplatform-powerpages-ccigiving.git
Powershell-CoxIntranet-Scripts,cox-cei/Powershell-CoxIntranet-Scripts,None,True,ASP.NET,2024-08-27T21:22:04Z,2025-05-22T01:15:17Z,2025-07-17T17:57:18Z,10636,1,1,0,3,main,False,False,https://github.com/cox-cei/Powershell-CoxIntranet-Scripts,https://github.com/cox-cei/Powershell-CoxIntranet-Scripts.git
RDA_CAI_Finance_BlackLineReporting_BlackLineReporting,cox-cei/RDA_CAI_Finance_BlackLineReporting_BlackLineReporting,None,True,,2024-08-09T16:08:30Z,2024-11-27T08:13:06Z,2024-11-27T08:13:04Z,12068,0,0,0,0,main,False,False,https://github.com/cox-cei/RDA_CAI_Finance_BlackLineReporting_BlackLineReporting,https://github.com/cox-cei/RDA_CAI_Finance_BlackLineReporting_BlackLineReporting.git
RDA_CAI_Retail_CMSTitleInquiry_TMSQuery,cox-cei/RDA_CAI_Retail_CMSTitleInquiry_TMSQuery,None,True,HTML,2024-08-09T16:01:57Z,2025-05-28T12:05:26Z,2025-05-28T12:05:23Z,10383,0,0,0,0,main,False,False,https://github.com/cox-cei/RDA_CAI_Retail_CMSTitleInquiry_TMSQuery,https://github.com/cox-cei/RDA_CAI_Retail_CMSTitleInquiry_TMSQuery.git
RDA_CAI_Retail_CMSTitleInquiry_TMSUpdate,cox-cei/RDA_CAI_Retail_CMSTitleInquiry_TMSUpdate,None,True,,2024-08-09T16:03:47Z,2025-05-28T12:06:09Z,2025-05-28T12:06:06Z,21447,0,0,0,0,main,False,False,https://github.com/cox-cei/RDA_CAI_Retail_CMSTitleInquiry_TMSUpdate,https://github.com/cox-cei/RDA_CAI_Retail_CMSTitleInquiry_TMSUpdate.git
RDA_CEI_HR_OhioWorkersComp_OhioWorkersComp,cox-cei/RDA_CEI_HR_OhioWorkersComp_OhioWorkersComp,None,True,,2024-08-09T16:09:51Z,2025-03-27T18:40:48Z,2025-03-27T18:40:45Z,35972,0,0,0,0,main,False,False,https://github.com/cox-cei/RDA_CEI_HR_OhioWorkersComp_OhioWorkersComp,https://github.com/cox-cei/RDA_CEI_HR_OhioWorkersComp_OhioWorkersComp.git
RDA_CEI_IBT_Condeco_MeetingCreator,cox-cei/RDA_CEI_IBT_Condeco_MeetingCreator,None,True,JavaScript,2024-08-09T16:06:00Z,2025-02-07T12:12:58Z,2025-02-07T12:12:54Z,48181,0,0,0,0,main,False,False,https://github.com/cox-cei/RDA_CEI_IBT_Condeco_MeetingCreator,https://github.com/cox-cei/RDA_CEI_IBT_Condeco_MeetingCreator.git
RDA_CEI_IBT_Condeco_MeetingMigrator,cox-cei/RDA_CEI_IBT_Condeco_MeetingMigrator,None,True,JavaScript,2024-08-09T16:09:06Z,2024-11-05T16:35:48Z,2024-11-08T09:14:37Z,34892,0,0,0,0,main,False,False,https://github.com/cox-cei/RDA_CEI_IBT_Condeco_MeetingMigrator,https://github.com/cox-cei/RDA_CEI_IBT_Condeco_MeetingMigrator.git
renaming-this-again,cox-cei/renaming-this-again,None,True,,2024-10-16T14:57:50Z,2024-10-19T00:36:21Z,2024-10-16T14:57:51Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/renaming-this-again,https://github.com/cox-cei/renaming-this-again.git
RPA-CAI-FIN-APPaymentExceptions-Dispatcher,cox-cei/RPA-CAI-FIN-APPaymentExceptions-Dispatcher,The dispatcher part of the bot that will put tickets from the ServiceStation into a queue,True,HTML,2024-07-08T21:07:38Z,2024-11-14T19:02:57Z,2025-01-09T17:11:26Z,3056,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-Dispatcher.git
RPA-CAI-FIN-APPaymentExceptions-OracleLibrary,cox-cei/RPA-CAI-FIN-APPaymentExceptions-OracleLibrary,"Library for all Oracle Screens, Ui Objects, and Descriptors",True,,2024-07-11T17:30:33Z,2024-12-04T13:08:30Z,2025-04-21T10:37:40Z,3762,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-OracleLibrary,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-OracleLibrary.git
RPA-CAI-FIN-APPaymentExceptions-Performer,cox-cei/RPA-CAI-FIN-APPaymentExceptions-Performer,The performer part of the bot that will match tickets from the ServiceStation to Oracle and work them in Wells Fargo,True,HTML,2024-07-08T21:07:40Z,2025-07-16T17:16:35Z,2025-07-16T17:16:33Z,109551,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-Performer,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-Performer.git
RPA-CAI-FIN-APPaymentExceptions-ServiceStationLibrary,cox-cei/RPA-CAI-FIN-APPaymentExceptions-ServiceStationLibrary,"Library for all ServiceStation Screens, Ui Objects, and Descriptors",True,,2024-07-11T17:30:35Z,2024-11-14T20:26:38Z,2024-11-14T20:26:34Z,43,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-ServiceStationLibrary,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-ServiceStationLibrary.git
RPA-CAI-FIN-APPaymentExceptions-WellsFargoLibrary,cox-cei/RPA-CAI-FIN-APPaymentExceptions-WellsFargoLibrary,"Library for all Wells Fargo Screens, Ui Objects, and Descriptors",True,,2024-07-11T17:30:37Z,2024-11-14T20:39:23Z,2025-02-27T06:46:14Z,8943,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-WellsFargoLibrary,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-WellsFargoLibrary.git
RPA-CAI-FIN-CreditMemoCAI-Dispatcher,cox-cei/RPA-CAI-FIN-CreditMemoCAI-Dispatcher,,True,HTML,2025-07-11T16:39:16Z,2025-07-17T13:33:13Z,2025-08-01T18:52:38Z,1758,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoCAI-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoCAI-Dispatcher.git
RPA-CAI-FIN-CreditMemoCAI-Performer,cox-cei/RPA-CAI-FIN-CreditMemoCAI-Performer,This is the Credit Memo CAI Performer,True,,2025-07-10T13:17:42Z,2025-07-28T18:15:53Z,2025-08-01T18:55:47Z,5145,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoCAI-Performer,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoCAI-Performer.git
RPA-CAI-FIN-CreditMemoIS-Dispatcher,cox-cei/RPA-CAI-FIN-CreditMemoIS-Dispatcher,,True,HTML,2025-07-11T16:39:33Z,2025-07-30T14:55:34Z,2025-08-01T18:52:49Z,1868,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoIS-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoIS-Dispatcher.git
RPA-CAI-FIN-CreditMemoIS-Performer,cox-cei/RPA-CAI-FIN-CreditMemoIS-Performer,This is the Credit Memo IS Performer,True,PowerShell,2025-07-10T14:56:35Z,2025-07-31T19:56:32Z,2025-08-02T20:54:30Z,5243,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoIS-Performer,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoIS-Performer.git
RPA-CAI-FIN-Disbursements-Dispatcher,cox-cei/RPA-CAI-FIN-Disbursements-Dispatcher,None,True,HTML,2024-08-29T14:23:32Z,2025-06-20T15:15:59Z,2025-06-20T15:15:56Z,1864,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Dispatcher.git
RPA-CAI-FIN-Disbursements-Oracle,cox-cei/RPA-CAI-FIN-Disbursements-Oracle,None,True,C#,2024-08-29T14:23:26Z,2025-01-09T16:47:57Z,2025-01-09T16:47:50Z,6808,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Oracle,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Oracle.git
RPA-CAI-FIN-Disbursements-Performer,cox-cei/RPA-CAI-FIN-Disbursements-Performer,None,True,C#,2024-08-29T14:23:28Z,2025-06-20T15:14:44Z,2025-06-20T15:14:40Z,112863,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Performer,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Performer.git
RPA-CAI-FIN-Disbursements-Performer-2,cox-cei/RPA-CAI-FIN-Disbursements-Performer-2,,True,HTML,2024-12-09T16:47:46Z,2025-06-20T15:15:21Z,2025-06-27T10:46:04Z,111733,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Performer-2,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Performer-2.git
RPA-CAI-FIN-Disbursements-Synergy,cox-cei/RPA-CAI-FIN-Disbursements-Synergy,None,True,,2024-08-29T14:23:33Z,2025-06-20T15:08:46Z,2025-06-20T15:08:42Z,8052,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Synergy,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Synergy.git
RPA-CAI-FIN-EAPSharedEmails-Dispatcher,cox-cei/RPA-CAI-FIN-EAPSharedEmails-Dispatcher,,True,HTML,2025-01-27T21:43:27Z,2025-02-05T16:13:19Z,2025-07-15T19:41:55Z,1491,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-EAPSharedEmails-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-EAPSharedEmails-Dispatcher.git
RPA-CAI-FIN-EAPSharedEmails-Performer,cox-cei/RPA-CAI-FIN-EAPSharedEmails-Performer,,True,HTML,2025-01-27T21:42:31Z,2025-02-04T15:41:31Z,2025-07-16T20:04:26Z,95709,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-EAPSharedEmails-Performer,https://github.com/cox-cei/RPA-CAI-FIN-EAPSharedEmails-Performer.git
RPA-CAI-FIN-FS-EmailApprovedROs-Performer,cox-cei/RPA-CAI-FIN-FS-EmailApprovedROs-Performer,,True,HTML,2025-06-10T15:28:35Z,2025-06-10T17:55:38Z,2025-07-09T14:22:55Z,2130,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-FS-EmailApprovedROs-Performer,https://github.com/cox-cei/RPA-CAI-FIN-FS-EmailApprovedROs-Performer.git
RPA-CAI-FIN-FS-EstimationApprovals-AutoIntegrate-Performer,cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-AutoIntegrate-Performer,,True,HTML,2025-06-10T15:43:44Z,2025-06-10T16:26:38Z,2025-07-07T15:02:27Z,1326,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-AutoIntegrate-Performer,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-AutoIntegrate-Performer.git
RPA-CAI-FIN-FS-EstimationApprovals-Dispatcher,cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-Dispatcher,,True,HTML,2025-06-10T15:43:47Z,2025-06-30T18:46:21Z,2025-07-18T19:04:24Z,1019,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-Dispatcher.git
RPA-CAI-FIN-FS-EstimationApprovals-Holman-Performer,cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-Holman-Performer,,True,HTML,2025-06-10T15:28:48Z,2025-06-30T19:21:16Z,2025-07-02T15:09:41Z,1373,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-Holman-Performer,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-Holman-Performer.git
RPA-CAI-FIN-FS-EstimationBilling-AutoIntegrate-Performer,cox-cei/RPA-CAI-FIN-FS-EstimationBilling-AutoIntegrate-Performer,fleet services estimation billing,True,HTML,2025-07-08T18:45:24Z,2025-07-10T18:55:58Z,2025-07-30T15:26:53Z,2337,0,0,0,1,dev,False,False,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationBilling-AutoIntegrate-Performer,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationBilling-AutoIntegrate-Performer.git
RPA-CAI-FIN-FS-EstimationBilling-Dispatcher,cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Dispatcher,,True,HTML,2025-07-02T12:39:03Z,2025-07-28T19:09:49Z,2025-07-28T19:09:47Z,2216,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Dispatcher.git
RPA-CAI-FIN-FS-EstimationBilling-Holman-Performer,cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Holman-Performer,fleet services estimation billing,True,HTML,2025-07-08T18:35:31Z,2025-07-09T19:47:51Z,2025-07-31T18:42:33Z,2319,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Holman-Performer,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Holman-Performer.git
RPA-CAI-FIN-FS-EstimationBilling-Performer,cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Performer,,True,,2025-07-02T13:31:30Z,2025-07-02T13:31:32Z,2025-07-02T13:31:30Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Performer,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Performer.git
RPA-CAI-FIN-InvoiceCancellation-Dispatcher,cox-cei/RPA-CAI-FIN-InvoiceCancellation-Dispatcher,This is the Dispatcher for Invoice Cancellations,True,HTML,2025-04-08T15:28:53Z,2025-07-24T20:34:58Z,2025-07-24T20:34:56Z,1007,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-InvoiceCancellation-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-InvoiceCancellation-Dispatcher.git
RPA-CAI-FIN-InvoiceCancellation-Performer,cox-cei/RPA-CAI-FIN-InvoiceCancellation-Performer,Performer bot for the Invoice Cancellation process. This project handles the Oracle invoice cancellation steps and appropriate updates servicenow tickets appropriately,True,HTML,2025-03-31T14:26:57Z,2025-04-08T15:18:06Z,2025-07-23T18:16:22Z,2727,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-InvoiceCancellation-Performer,https://github.com/cox-cei/RPA-CAI-FIN-InvoiceCancellation-Performer.git
RPA-CAI-FIN-Karmak-addunits-library,cox-cei/RPA-CAI-FIN-Karmak-addunits-library,Library containing object repository and custom activities for interacting with the Karmak application. Used in the Karmak Add Units automation.,True,,2025-02-26T17:53:40Z,2025-02-26T18:22:01Z,2025-04-22T17:41:40Z,5128,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-addunits-library,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-addunits-library.git
RPA-CAI-FIN-Karmak-AddUnits-Performer,cox-cei/RPA-CAI-FIN-Karmak-AddUnits-Performer,Automatiopn used to input VIN given in transaction item into VINDecoder website and retrieve vehicle details. The bot then uses the vehicle details to add new units in the Karmak application.,True,HTML,2025-02-12T18:32:18Z,2025-07-24T16:09:38Z,2025-07-24T16:09:35Z,2976,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-AddUnits-Performer,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-AddUnits-Performer.git
RPA-CAI-FIN-Karmak-Amazon-Dispatcher,cox-cei/RPA-CAI-FIN-Karmak-Amazon-Dispatcher,,True,HTML,2025-04-15T14:34:58Z,2025-06-06T17:40:29Z,2025-07-30T15:41:17Z,1506,0,0,0,2,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-Amazon-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-Amazon-Dispatcher.git
RPA-CAI-FIN-Karmak-Amazon-Performer,cox-cei/RPA-CAI-FIN-Karmak-Amazon-Performer,,True,HTML,2025-04-15T14:34:32Z,2025-07-01T14:29:28Z,2025-07-01T14:51:05Z,5121,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-Amazon-Performer,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-Amazon-Performer.git
RPA-CAI-FIN-Karmak-EstimationApprovals-Dispatcher,cox-cei/RPA-CAI-FIN-Karmak-EstimationApprovals-Dispatcher,,True,,2025-04-15T14:35:37Z,2025-05-12T14:23:35Z,2025-06-23T19:33:47Z,1104,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-EstimationApprovals-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-EstimationApprovals-Dispatcher.git
RPA-CAI-FIN-Karmak-EstimationApprovals-Performer,cox-cei/RPA-CAI-FIN-Karmak-EstimationApprovals-Performer,,True,HTML,2025-04-15T14:35:13Z,2025-05-28T16:15:33Z,2025-06-23T19:04:55Z,1602,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-EstimationApprovals-Performer,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-EstimationApprovals-Performer.git
RPA-CAI-FIN-Karmak-FileDispatcher,cox-cei/RPA-CAI-FIN-Karmak-FileDispatcher,Process used to loop through files in the New folder of CAI sharepoint and capture the file details as transaction items in our queue,True,HTML,2025-02-12T18:26:36Z,2025-02-14T15:26:37Z,2025-05-05T20:44:58Z,979,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-FileDispatcher,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-FileDispatcher.git
RPA-CAI-FIN-Karmak-VIN-Dispatcher,cox-cei/RPA-CAI-FIN-Karmak-VIN-Dispatcher,Process used to ingest the sharepoint files which the File Dispatcher created as queue items. The bot will read through the given excel files and create a transaction item for each row in the file,True,HTML,2025-02-11T18:41:59Z,2025-05-06T15:05:22Z,2025-05-06T15:05:19Z,1039,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-VIN-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-VIN-Dispatcher.git
RPA-CAI-FIN-Karmak-VINDecoder-Library,cox-cei/RPA-CAI-FIN-Karmak-VINDecoder-Library,Object repository and custom activity library for navigating to and interacting with www.decodethevin.com,True,C#,2025-02-12T18:32:30Z,2025-02-17T20:11:51Z,2025-07-29T17:22:22Z,5103,0,0,0,2,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-VINDecoder-Library,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-VINDecoder-Library.git
RPA-CAI-FIN-ManheimRedemption-Dispatcher,cox-cei/RPA-CAI-FIN-ManheimRedemption-Dispatcher,,True,HTML,2025-07-24T13:32:28Z,2025-07-24T16:54:50Z,2025-07-24T16:54:46Z,970,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-ManheimRedemption-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-ManheimRedemption-Dispatcher.git
RPA-CAI-FIN-ManheimRedemption-performer,cox-cei/RPA-CAI-FIN-ManheimRedemption-performer,Performer bot for Manheim Redemption Automation,True,,2025-07-24T14:08:45Z,2025-07-25T15:42:29Z,2025-07-25T15:42:25Z,8160,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-ManheimRedemption-performer,https://github.com/cox-cei/RPA-CAI-FIN-ManheimRedemption-performer.git
RPA-CAI-FIN-Rush-ConfirmPaymentPerformer,cox-cei/RPA-CAI-FIN-Rush-ConfirmPaymentPerformer,,True,HTML,2024-12-06T19:35:54Z,2025-07-02T14:43:55Z,2025-07-02T14:43:51Z,1995,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Rush-ConfirmPaymentPerformer,https://github.com/cox-cei/RPA-CAI-FIN-Rush-ConfirmPaymentPerformer.git
RPA-CAI-FIN-Rush-Dispatcher,cox-cei/RPA-CAI-FIN-Rush-Dispatcher,,True,HTML,2024-10-22T15:16:25Z,2025-02-05T13:11:47Z,2025-05-14T14:51:51Z,9840,0,0,0,0,dev,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Dispatcher.git
RPA-CAI-FIN-Rush-Oracle,cox-cei/RPA-CAI-FIN-Rush-Oracle,,True,,2024-10-22T15:21:37Z,2024-11-19T13:20:08Z,2025-03-26T15:33:57Z,16170,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Oracle,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Oracle.git
RPA-CAI-FIN-Rush-Performer,cox-cei/RPA-CAI-FIN-Rush-Performer,,True,HTML,2024-10-22T15:19:41Z,2025-06-23T15:08:48Z,2025-07-21T17:58:47Z,112647,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Performer,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Performer.git
RPA-CAI-FIN-Rush-Performer-2,cox-cei/RPA-CAI-FIN-Rush-Performer-2,,True,,2025-01-10T17:06:53Z,2025-01-10T17:06:55Z,2025-01-10T17:06:53Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Performer-2,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Performer-2.git
RPA-CAI-FIN-Rush-ServiceNowLibrary,cox-cei/RPA-CAI-FIN-Rush-ServiceNowLibrary,Library containing prebuilt API activities used to get and patch ServiceNow backend API,True,,2024-12-03T16:41:09Z,2025-02-03T16:22:47Z,2025-03-05T19:25:14Z,165,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Rush-ServiceNowLibrary,https://github.com/cox-cei/RPA-CAI-FIN-Rush-ServiceNowLibrary.git
RPA-CAI-FS-AutoIntegrate-Library,cox-cei/RPA-CAI-FS-AutoIntegrate-Library,Object Repository Library for AutoIntegrate Portal for FS Estimation Approvals,True,,2025-05-09T12:45:13Z,2025-05-19T19:09:01Z,2025-07-16T15:13:28Z,2249,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FS-AutoIntegrate-Library,https://github.com/cox-cei/RPA-CAI-FS-AutoIntegrate-Library.git
RPA-CAI-FS-Holman-Library,cox-cei/RPA-CAI-FS-Holman-Library,Object Repository Library for Holman Portal for FS Estimation Approvals,True,,2025-05-09T12:37:55Z,2025-05-14T15:04:33Z,2025-07-09T15:55:36Z,1338,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FS-Holman-Library,https://github.com/cox-cei/RPA-CAI-FS-Holman-Library.git
RPA-CAI-FS-PreBiller-Dispatcher,cox-cei/RPA-CAI-FS-PreBiller-Dispatcher,RPA-CAI-FS-PreBiller-Dispatcher,True,HTML,2025-07-24T14:00:33Z,2025-07-25T15:17:47Z,2025-07-25T15:17:42Z,1589,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FS-PreBiller-Dispatcher,https://github.com/cox-cei/RPA-CAI-FS-PreBiller-Dispatcher.git
RPA-CAI-FS-PreBiller-Performer,cox-cei/RPA-CAI-FS-PreBiller-Performer,Fleet services Pre-Filler Performer,True,HTML,2025-07-24T13:56:32Z,2025-07-25T20:29:11Z,2025-08-01T18:46:21Z,3467,0,0,0,2,main,False,False,https://github.com/cox-cei/RPA-CAI-FS-PreBiller-Performer,https://github.com/cox-cei/RPA-CAI-FS-PreBiller-Performer.git
RPA-CAI-Manheim-VinReconciliationReportingPerformer,cox-cei/RPA-CAI-Manheim-VinReconciliationReportingPerformer,Manheim Vin Reconciliation reporting automation ,True,,2025-04-18T14:27:22Z,2025-04-18T14:27:24Z,2025-04-18T14:27:23Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-Manheim-VinReconciliationReportingPerformer,https://github.com/cox-cei/RPA-CAI-Manheim-VinReconciliationReportingPerformer.git
RPA-CAI-VinSolutions-DealerOnboarding-AdditionalConfiguration-Performer,cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-AdditionalConfiguration-Performer,This is the second performer for the Vinsolutions DealerOnboarding Process that handles the additional configuration of the dealer after the dealer instance has already been created,True,HTML,2025-04-08T15:28:30Z,2025-06-09T17:41:54Z,2025-07-30T15:07:19Z,8382,0,0,0,3,main,False,False,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-AdditionalConfiguration-Performer,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-AdditionalConfiguration-Performer.git
RPA-CAI-VinSolutions-DealerOnboarding-Dispatcher,cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Dispatcher,A UiPath dispatcher process that reads onboarding emails for VinSolutions and populates a queue for processing.,True,HTML,2024-10-02T15:41:36Z,2025-01-16T15:03:21Z,2025-01-16T15:03:22Z,1574,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Dispatcher,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Dispatcher.git
RPA-CAI-VinSolutions-DealerOnboarding-Performer,cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Performer,A UiPath performer that processes onboarding requests for new dealers on VinSolutions.,True,HTML,2024-10-02T15:41:38Z,2025-05-21T16:22:49Z,2025-08-01T20:44:59Z,12120,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Performer,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Performer.git
RPA-CAI-VinSolutions-DealerOnboarding-Reporter,cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Reporter,A UiPath reporter process that generates a report based on onboarding requests for new dealers on VinSolutions.,True,HTML,2024-10-16T18:27:45Z,2025-06-02T12:28:28Z,2025-06-02T12:28:25Z,2071,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Reporter,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Reporter.git
RPA-CAI-VinSolutions-Descriptors,cox-cei/RPA-CAI-VinSolutions-Descriptors,Descriptors library for VinSolutions,True,,2025-05-30T13:14:44Z,2025-07-30T14:00:52Z,2025-08-01T17:53:47Z,4539,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-VinSolutions-Descriptors,https://github.com/cox-cei/RPA-CAI-VinSolutions-Descriptors.git
RPA-CAI-VinSolutions-FeatureEnablement-Dispatcher,cox-cei/RPA-CAI-VinSolutions-FeatureEnablement-Dispatcher,Dispatcher subprocess of RPA-CAI-VinSolutions-FeatureEnablement,True,,2025-06-16T14:15:54Z,2025-06-16T20:09:11Z,2025-07-31T18:32:55Z,1363,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-VinSolutions-FeatureEnablement-Dispatcher,https://github.com/cox-cei/RPA-CAI-VinSolutions-FeatureEnablement-Dispatcher.git
RPA-CAI-VinSolutions-FeatureEnablement-Performer,cox-cei/RPA-CAI-VinSolutions-FeatureEnablement-Performer,Performer subprocess of RPA-CAI-VinSolutions-FeatureEnablement,True,,2025-06-16T14:16:21Z,2025-06-17T17:57:33Z,2025-07-31T17:25:48Z,2525,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-VinSolutions-FeatureEnablement-Performer,https://github.com/cox-cei/RPA-CAI-VinSolutions-FeatureEnablement-Performer.git
RPA-CCI-PDC-CERTIFICATE,cox-cei/RPA-CCI-PDC-CERTIFICATE,Generate PDC Certtificate using Certify Tax,True,HTML,2025-05-12T18:58:02Z,2025-05-14T20:47:18Z,2025-07-22T12:33:53Z,3225,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CCI-PDC-CERTIFICATE,https://github.com/cox-cei/RPA-CCI-PDC-CERTIFICATE.git
RPA-CCI-PDC-CERTIFICATE-REPORTER,cox-cei/RPA-CCI-PDC-CERTIFICATE-REPORTER,Reporter subprocess for the RPA-CCI-PDC-CERTIFICATE process,True,HTML,2025-05-22T18:17:22Z,2025-05-22T19:55:15Z,2025-06-02T19:59:35Z,1052,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CCI-PDC-CERTIFICATE-REPORTER,https://github.com/cox-cei/RPA-CCI-PDC-CERTIFICATE-REPORTER.git
RPA-CEI-Azure-Document-Understanding-Poc,cox-cei/RPA-CEI-Azure-Document-Understanding-Poc,,True,,2025-01-29T20:56:26Z,2025-01-29T20:56:28Z,2025-01-29T20:56:27Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Azure-Document-Understanding-Poc,https://github.com/cox-cei/RPA-CEI-Azure-Document-Understanding-Poc.git
RPA-CEI-eDiscovery-Legal-OnitUpdate-Performer,cox-cei/RPA-CEI-eDiscovery-Legal-OnitUpdate-Performer,A UiPath Performer for updating the Onit Resolution Details and Case ID by making a singular Api call. It gets the atom ID and Case ID from the Queue.,True,HTML,2024-09-17T18:59:59Z,2025-02-05T15:15:50Z,2025-02-05T15:15:46Z,1092,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-eDiscovery-Legal-OnitUpdate-Performer,https://github.com/cox-cei/RPA-CEI-eDiscovery-Legal-OnitUpdate-Performer.git
RPA-CEI-FIN-VentivUserManagement-Dispatcher,cox-cei/RPA-CEI-FIN-VentivUserManagement-Dispatcher,None,True,,2024-07-11T20:42:19Z,2024-12-10T20:59:05Z,2024-12-10T20:59:01Z,17332,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-FIN-VentivUserManagement-Dispatcher,https://github.com/cox-cei/RPA-CEI-FIN-VentivUserManagement-Dispatcher.git
RPA-CEI-FIN-VentivUserManagement-LoginLibrary,cox-cei/RPA-CEI-FIN-VentivUserManagement-LoginLibrary,None,True,,2024-07-11T20:42:15Z,2024-12-10T16:45:31Z,2024-12-10T16:45:28Z,1135,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-FIN-VentivUserManagement-LoginLibrary,https://github.com/cox-cei/RPA-CEI-FIN-VentivUserManagement-LoginLibrary.git
RPA-CEI-FIN-VentivUserManagement-Performer,cox-cei/RPA-CEI-FIN-VentivUserManagement-Performer,None,True,HTML,2024-07-11T20:42:17Z,2024-11-08T21:52:13Z,2025-01-06T20:46:23Z,11099,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-FIN-VentivUserManagement-Performer,https://github.com/cox-cei/RPA-CEI-FIN-VentivUserManagement-Performer.git
RPA-CEI-Finance-Diligent-UI-Library,cox-cei/RPA-CEI-Finance-Diligent-UI-Library,Library to be used by the Diligent Bots,True,,2025-07-18T15:24:17Z,2025-07-29T16:29:31Z,2025-08-01T20:58:53Z,1434,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Diligent-UI-Library,https://github.com/cox-cei/RPA-CEI-Finance-Diligent-UI-Library.git
RPA-CEI-Finance-Tax-Diligent-Company-Profile-Dispatcher,cox-cei/RPA-CEI-Finance-Tax-Diligent-Company-Profile-Dispatcher,,True,,2025-07-18T12:27:03Z,2025-07-29T16:30:07Z,2025-07-29T16:30:17Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Diligent-Company-Profile-Dispatcher,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Diligent-Company-Profile-Dispatcher.git
RPA-CEI-Finance-Tax-Diligent-Unitary-Dispatcher,cox-cei/RPA-CEI-Finance-Tax-Diligent-Unitary-Dispatcher,,True,,2025-07-18T12:24:53Z,2025-07-18T12:24:55Z,2025-07-18T12:24:54Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Diligent-Unitary-Dispatcher,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Diligent-Unitary-Dispatcher.git
RPA-CEI-Finance-Tax-Diligent-Unitary-Performer,cox-cei/RPA-CEI-Finance-Tax-Diligent-Unitary-Performer,,True,,2025-07-18T12:24:27Z,2025-07-18T12:24:29Z,2025-07-18T12:24:28Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Diligent-Unitary-Performer,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Diligent-Unitary-Performer.git
RPA-CEI-Finance-Tax-InvoiceRetrieval-Dispatcher,cox-cei/RPA-CEI-Finance-Tax-InvoiceRetrieval-Dispatcher,Dispatcher process for the Invoice Retrieval automation.,True,HTML,2025-06-04T20:35:56Z,2025-07-24T17:56:59Z,2025-07-31T15:12:20Z,1022,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-InvoiceRetrieval-Dispatcher,https://github.com/cox-cei/RPA-CEI-Finance-Tax-InvoiceRetrieval-Dispatcher.git
RPA-CEI-Finance-Tax-InvoiceRetrieval-Performer,cox-cei/RPA-CEI-Finance-Tax-InvoiceRetrieval-Performer,Performer process for the Invoice Retrieval automation.,True,,2025-06-04T20:36:12Z,2025-06-05T12:57:21Z,2025-07-31T17:52:53Z,2236,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-InvoiceRetrieval-Performer,https://github.com/cox-cei/RPA-CEI-Finance-Tax-InvoiceRetrieval-Performer.git
RPA-CEI-Finance-Tax-NOLanVA-Dispatcher,cox-cei/RPA-CEI-Finance-Tax-NOLanVA-Dispatcher,Dispatcher process for the NOLan VA automation,True,,2025-07-08T13:38:57Z,2025-07-11T19:12:49Z,2025-07-29T12:37:34Z,964,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-NOLanVA-Dispatcher,https://github.com/cox-cei/RPA-CEI-Finance-Tax-NOLanVA-Dispatcher.git
RPA-CEI-Finance-Tax-NOLanVA-Performer,cox-cei/RPA-CEI-Finance-Tax-NOLanVA-Performer,Performer process for the NOLan VA automation,True,,2025-07-08T13:32:22Z,2025-07-11T19:13:10Z,2025-08-01T14:32:40Z,1772,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-NOLanVA-Performer,https://github.com/cox-cei/RPA-CEI-Finance-Tax-NOLanVA-Performer.git
RPA-CEI-Finance-Tax-Reporter,cox-cei/RPA-CEI-Finance-Tax-Reporter,Reporting process used for the CEI Tax automations.,True,,2025-06-19T17:30:16Z,2025-06-24T13:49:58Z,2025-07-31T17:50:20Z,1055,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Reporter,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Reporter.git
RPA-CEI-IA-CoxQueueReporter-Library,cox-cei/RPA-CEI-IA-CoxQueueReporter-Library,Library used for the IA Queue Reporter,True,,2025-06-16T14:15:43Z,2025-06-16T14:15:45Z,2025-06-16T14:15:44Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-IA-CoxQueueReporter-Library,https://github.com/cox-cei/RPA-CEI-IA-CoxQueueReporter-Library.git
RPA-CEI-IA-Support-PasswordManagement-Dispatcher,cox-cei/RPA-CEI-IA-Support-PasswordManagement-Dispatcher,,True,HTML,2025-07-16T15:51:16Z,2025-07-21T17:05:11Z,2025-07-22T14:21:17Z,1096,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-IA-Support-PasswordManagement-Dispatcher,https://github.com/cox-cei/RPA-CEI-IA-Support-PasswordManagement-Dispatcher.git
RPA-CEI-IA-Support-PasswordManagement-ExpirationEmailCleanUp,cox-cei/RPA-CEI-IA-Support-PasswordManagement-ExpirationEmailCleanUp,Password Management 2nd Performer to look for all open action center items that need to be emailed out to Infrastructure Team that morning,True,HTML,2025-07-21T14:48:58Z,2025-07-25T18:58:41Z,2025-07-29T13:36:10Z,1206,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-IA-Support-PasswordManagement-ExpirationEmailCleanUp,https://github.com/cox-cei/RPA-CEI-IA-Support-PasswordManagement-ExpirationEmailCleanUp.git
RPA-CEI-IA-Support-PasswordManagement-Performer,cox-cei/RPA-CEI-IA-Support-PasswordManagement-Performer,,True,HTML,2025-06-10T17:15:24Z,2025-07-14T15:31:17Z,2025-07-23T15:07:01Z,1514,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-IA-Support-PasswordManagement-Performer,https://github.com/cox-cei/RPA-CEI-IA-Support-PasswordManagement-Performer.git
RPA-CEI-IA-Support-Ticket-Performer,cox-cei/RPA-CEI-IA-Support-Ticket-Performer,UiPath Orchestrator Alert Intergration to Service Now,True,,2024-10-21T20:11:51Z,2024-11-18T15:52:03Z,2024-12-13T16:31:22Z,1007,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-IA-Support-Ticket-Performer,https://github.com/cox-cei/RPA-CEI-IA-Support-Ticket-Performer.git
RPA-CEI-Legal-eDiscovery-BigFix-Performer,cox-cei/RPA-CEI-Legal-eDiscovery-BigFix-Performer,A UiPath This performer for retrieves the CEI Big Fix file from Sharepoint and adds it to the Storage Bucket in the UiPath Orchestrator.,True,HTML,2024-09-26T21:20:18Z,2024-12-17T20:18:51Z,2024-12-17T20:18:44Z,95661,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-BigFix-Performer,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-BigFix-Performer.git
RPA-CEI-Legal-eDiscovery-ComplianceCenter-Performer,cox-cei/RPA-CEI-Legal-eDiscovery-ComplianceCenter-Performer,This is the Performer process that creates a hold case and hold in CEI/CAI Compliance Center (Microsoft Purview).,True,HTML,2024-09-26T21:20:12Z,2024-12-17T20:09:03Z,2025-02-04T19:32:58Z,95903,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-ComplianceCenter-Performer,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-ComplianceCenter-Performer.git
RPA-CEI-Legal-eDiscovery-Dispatcher,cox-cei/RPA-CEI-Legal-eDiscovery-Dispatcher,RPA bot that retrieves pending requests from Onit and adds them to a queue for processing.,True,HTML,2024-07-30T17:38:14Z,2025-02-05T15:23:45Z,2025-02-05T15:23:40Z,257,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-Dispatcher,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-Dispatcher.git
RPA-CEI-Legal-eDiscovery-EDACM-Performer,cox-cei/RPA-CEI-Legal-eDiscovery-EDACM-Performer,None,True,HTML,2024-09-13T16:28:45Z,2025-02-05T15:21:23Z,2025-02-05T15:21:19Z,95742,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-EDACM-Performer,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-EDACM-Performer.git
RPA-CEI-Legal-eDiscovery-FTK-performer,cox-cei/RPA-CEI-Legal-eDiscovery-FTK-performer,This is the Performer process that generates a new case number and creates a case in FTK. The case details are then added to a queue for downstream processing.,True,HTML,2024-08-19T19:36:52Z,2025-02-05T15:52:27Z,2025-02-07T20:44:13Z,1590,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-FTK-performer,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-FTK-performer.git
RPA-CEI-Legal-eDiscovery-LegalHoldPro-Performer,cox-cei/RPA-CEI-Legal-eDiscovery-LegalHoldPro-Performer,This is the Performer process that creates a new hold in LegalHoldPro and adds relevant custodian and hold viewer information. This process also adds a hold notice to LHP if applicable.,True,HTML,2024-09-26T21:20:16Z,2025-06-27T13:44:21Z,2025-06-27T13:44:16Z,97850,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-LegalHoldPro-Performer,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-LegalHoldPro-Performer.git
RPA-CEI-Legal-eDiscovery-Onit-Performer,cox-cei/RPA-CEI-Legal-eDiscovery-Onit-Performer,RPA bot that updates pending requests in Onit.,True,HTML,2024-07-30T17:38:16Z,2025-02-05T15:10:46Z,2025-03-17T14:13:36Z,96200,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-Onit-Performer,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-Onit-Performer.git
RPA-CEI-Legal-eDiscovery-Reporter,cox-cei/RPA-CEI-Legal-eDiscovery-Reporter,None,True,HTML,2024-09-26T21:20:10Z,2024-12-17T19:45:56Z,2024-12-17T19:45:47Z,96324,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-Reporter,https://github.com/cox-cei/RPA-CEI-Legal-eDiscovery-Reporter.git
RPA-CEI-Legal-Onit-UAT-UI-Library,cox-cei/RPA-CEI-Legal-Onit-UAT-UI-Library,UI Library for the sandbox Onit environment,True,,2024-10-30T20:50:15Z,2024-11-04T12:08:45Z,2024-11-04T12:11:06Z,51,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Legal-Onit-UAT-UI-Library,https://github.com/cox-cei/RPA-CEI-Legal-Onit-UAT-UI-Library.git
RPA-CEI-Legal-SubpoenaResponseLetter-Dispatcher,cox-cei/RPA-CEI-Legal-SubpoenaResponseLetter-Dispatcher,The dispatcher process for the Legal Subpoena Response Letter automation.,True,C#,2025-03-31T14:13:19Z,2025-05-27T12:52:34Z,2025-05-27T13:10:44Z,971,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Legal-SubpoenaResponseLetter-Dispatcher,https://github.com/cox-cei/RPA-CEI-Legal-SubpoenaResponseLetter-Dispatcher.git
RPA-CEI-Legal-SubpoenaResponseLetter-Performer,cox-cei/RPA-CEI-Legal-SubpoenaResponseLetter-Performer,The performer process for the Legal Subpoena Response Letter automation.,True,HTML,2024-11-19T18:05:19Z,2025-05-27T12:52:07Z,2025-05-27T13:10:40Z,1609,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Legal-SubpoenaResponseLetter-Performer,https://github.com/cox-cei/RPA-CEI-Legal-SubpoenaResponseLetter-Performer.git
RPA-CEI-MonitorTestCases-Performer,cox-cei/RPA-CEI-MonitorTestCases-Performer,Test Case Monitor,True,,2025-04-02T12:14:16Z,2025-04-02T12:14:18Z,2025-04-02T12:14:16Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-MonitorTestCases-Performer,https://github.com/cox-cei/RPA-CEI-MonitorTestCases-Performer.git
RPA-CEI-OrchestratorAPI,cox-cei/RPA-CEI-OrchestratorAPI,Orchestrator API,True,,2025-04-02T12:10:05Z,2025-04-02T12:10:08Z,2025-04-02T12:10:06Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-OrchestratorAPI,https://github.com/cox-cei/RPA-CEI-OrchestratorAPI.git
rpa-uipath-library-pipeline,cox-cei/rpa-uipath-library-pipeline,Repo that will build out UIPath libraries with a CI/CD pipeline using GitHub Actions,True,,2025-07-18T12:31:17Z,2025-07-24T13:38:02Z,2025-08-04T00:19:47Z,53,0,0,0,0,main,False,False,https://github.com/cox-cei/rpa-uipath-library-pipeline,https://github.com/cox-cei/rpa-uipath-library-pipeline.git
rpa-uipath-pipeline-demo,cox-cei/rpa-uipath-pipeline-demo,Repo to look into UIPath Orchestrator's Automation OPS,True,Python,2025-05-16T14:41:27Z,2025-08-02T20:36:39Z,2025-08-02T20:36:37Z,123365,0,0,0,0,main,False,False,https://github.com/cox-cei/rpa-uipath-pipeline-demo,https://github.com/cox-cei/rpa-uipath-pipeline-demo.git
RPA_CAI_Finance_APCoding_Dispatcher,cox-cei/RPA_CAI_Finance_APCoding_Dispatcher,None,True,,2024-08-09T16:00:29Z,2025-07-24T12:59:42Z,2025-07-24T12:59:39Z,1244,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Finance_APCoding_Dispatcher,https://github.com/cox-cei/RPA_CAI_Finance_APCoding_Dispatcher.git
RPA_CAI_Finance_APCoding_Objects,cox-cei/RPA_CAI_Finance_APCoding_Objects,None,True,,2024-08-09T16:09:18Z,2024-11-14T23:09:19Z,2025-05-07T15:43:41Z,1750,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Finance_APCoding_Objects,https://github.com/cox-cei/RPA_CAI_Finance_APCoding_Objects.git
RPA_CAI_Finance_APCoding_Performer,cox-cei/RPA_CAI_Finance_APCoding_Performer,None,True,,2024-08-09T16:10:49Z,2025-07-15T12:09:02Z,2025-07-22T14:54:33Z,16903,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA_CAI_Finance_APCoding_Performer,https://github.com/cox-cei/RPA_CAI_Finance_APCoding_Performer.git
RPA_CAI_Finance_ISAPCoding_Dispatcher,cox-cei/RPA_CAI_Finance_ISAPCoding_Dispatcher,None,True,,2024-08-09T16:04:31Z,2025-07-15T07:51:34Z,2025-07-15T07:51:31Z,1294,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Finance_ISAPCoding_Dispatcher,https://github.com/cox-cei/RPA_CAI_Finance_ISAPCoding_Dispatcher.git
RPA_CAI_Finance_ISAPCoding_Objects,cox-cei/RPA_CAI_Finance_ISAPCoding_Objects,None,True,,2024-08-09T16:02:10Z,2024-10-18T23:05:14Z,2024-11-14T23:12:43Z,1748,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Finance_ISAPCoding_Objects,https://github.com/cox-cei/RPA_CAI_Finance_ISAPCoding_Objects.git
RPA_CAI_Finance_ISAPCoding_Performer,cox-cei/RPA_CAI_Finance_ISAPCoding_Performer,None,True,,2024-08-09T16:01:15Z,2025-07-17T13:28:53Z,2025-07-29T15:43:22Z,67422,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Finance_ISAPCoding_Performer,https://github.com/cox-cei/RPA_CAI_Finance_ISAPCoding_Performer.git
RPA_CAI_Finance_ISAPCoding_Reporter,cox-cei/RPA_CAI_Finance_ISAPCoding_Reporter,None,True,,2024-08-09T16:02:08Z,2025-06-26T14:19:14Z,2025-06-26T14:19:10Z,973,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Finance_ISAPCoding_Reporter,https://github.com/cox-cei/RPA_CAI_Finance_ISAPCoding_Reporter.git
RPA_CAI_Mobility_FSUploadInvoice_BibnetPerformer,cox-cei/RPA_CAI_Mobility_FSUploadInvoice_BibnetPerformer,None,True,,2024-08-09T16:08:19Z,2024-11-26T19:21:01Z,2024-12-30T18:20:54Z,18168,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Mobility_FSUploadInvoice_BibnetPerformer,https://github.com/cox-cei/RPA_CAI_Mobility_FSUploadInvoice_BibnetPerformer.git
RPA_CAI_Mobility_FSUploadInvoice_CoupaActivities,cox-cei/RPA_CAI_Mobility_FSUploadInvoice_CoupaActivities,None,True,,2024-08-09T16:02:03Z,2024-11-26T19:19:22Z,2025-01-22T20:26:45Z,3032,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Mobility_FSUploadInvoice_CoupaActivities,https://github.com/cox-cei/RPA_CAI_Mobility_FSUploadInvoice_CoupaActivities.git
RPA_CAI_Mobility_FSUploadInvoice_CoupaPerformer,cox-cei/RPA_CAI_Mobility_FSUploadInvoice_CoupaPerformer,None,True,,2024-08-09T16:09:16Z,2024-11-26T19:19:59Z,2025-01-24T16:51:14Z,2899,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Mobility_FSUploadInvoice_CoupaPerformer,https://github.com/cox-cei/RPA_CAI_Mobility_FSUploadInvoice_CoupaPerformer.git
RPA_CAI_Mobility_FSUploadInvoice_DailyReport,cox-cei/RPA_CAI_Mobility_FSUploadInvoice_DailyReport,None,True,HTML,2024-08-09T16:01:17Z,2024-11-26T19:22:43Z,2024-11-26T19:22:40Z,1405,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Mobility_FSUploadInvoice_DailyReport,https://github.com/cox-cei/RPA_CAI_Mobility_FSUploadInvoice_DailyReport.git
RPA_CAI_Mobility_FSUploadInvoice_KarmakActivities,cox-cei/RPA_CAI_Mobility_FSUploadInvoice_KarmakActivities,None,True,,2024-08-09T16:00:25Z,2025-07-30T15:00:35Z,2025-08-01T15:57:50Z,28381,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Mobility_FSUploadInvoice_KarmakActivities,https://github.com/cox-cei/RPA_CAI_Mobility_FSUploadInvoice_KarmakActivities.git
RPA_CAI_Mobility_FSUploadInvoice_KarmakDispatcher,cox-cei/RPA_CAI_Mobility_FSUploadInvoice_KarmakDispatcher,None,True,,2024-08-09T16:07:46Z,2024-11-26T19:16:58Z,2024-12-30T19:35:52Z,26926,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Mobility_FSUploadInvoice_KarmakDispatcher,https://github.com/cox-cei/RPA_CAI_Mobility_FSUploadInvoice_KarmakDispatcher.git
RPA_CAI_Mobility_FSUploadInvoice_MichelinActivities,cox-cei/RPA_CAI_Mobility_FSUploadInvoice_MichelinActivities,None,True,,2024-08-09T16:02:06Z,2024-11-26T19:21:52Z,2024-11-26T19:21:49Z,3886,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Mobility_FSUploadInvoice_MichelinActivities,https://github.com/cox-cei/RPA_CAI_Mobility_FSUploadInvoice_MichelinActivities.git
RPA_CAI_Retail_CMSTitleInquiry_DisplayQueueCount,cox-cei/RPA_CAI_Retail_CMSTitleInquiry_DisplayQueueCount,,True,,2024-11-08T15:07:35Z,2024-11-08T15:07:37Z,2024-11-08T15:07:35Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Retail_CMSTitleInquiry_DisplayQueueCount,https://github.com/cox-cei/RPA_CAI_Retail_CMSTitleInquiry_DisplayQueueCount.git
RPA_CAI_Retail_CMSTitleInquiry_FromTU,cox-cei/RPA_CAI_Retail_CMSTitleInquiry_FromTU,None,True,C#,2024-08-09T16:02:47Z,2025-06-02T20:41:55Z,2025-06-02T20:41:52Z,3680,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Retail_CMSTitleInquiry_FromTU,https://github.com/cox-cei/RPA_CAI_Retail_CMSTitleInquiry_FromTU.git
RPA_CAI_Retail_CMSTitleInquiry_ImageArchive,cox-cei/RPA_CAI_Retail_CMSTitleInquiry_ImageArchive,None,True,HTML,2024-08-09T16:08:26Z,2025-01-06T12:16:54Z,2025-01-06T12:16:51Z,1745,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Retail_CMSTitleInquiry_ImageArchive,https://github.com/cox-cei/RPA_CAI_Retail_CMSTitleInquiry_ImageArchive.git
RPA_CAI_Retail_CMSTitleInquiry_KarmakLibrary,cox-cei/RPA_CAI_Retail_CMSTitleInquiry_KarmakLibrary,None,True,,2024-08-09T16:03:31Z,2024-10-18T23:09:04Z,2024-08-09T16:03:32Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Retail_CMSTitleInquiry_KarmakLibrary,https://github.com/cox-cei/RPA_CAI_Retail_CMSTitleInquiry_KarmakLibrary.git
RPA_CAI_Retail_CMSTitleInquiry_TitleManagementSystemActivities,cox-cei/RPA_CAI_Retail_CMSTitleInquiry_TitleManagementSystemActivities,None,True,,2024-08-09T16:07:31Z,2025-05-28T12:04:39Z,2025-07-25T13:33:43Z,6501,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Retail_CMSTitleInquiry_TitleManagementSystemActivities,https://github.com/cox-cei/RPA_CAI_Retail_CMSTitleInquiry_TitleManagementSystemActivities.git
RPA_CAI_Retail_CMSTitleInquiry_ToTU,cox-cei/RPA_CAI_Retail_CMSTitleInquiry_ToTU,None,True,,2024-08-09T16:06:55Z,2025-01-21T16:18:30Z,2025-01-21T16:18:28Z,1244,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Retail_CMSTitleInquiry_ToTU,https://github.com/cox-cei/RPA_CAI_Retail_CMSTitleInquiry_ToTU.git
RPA_CAI_Retail_CMSTitleInquiry_ToTUFromTUReconcilliation,cox-cei/RPA_CAI_Retail_CMSTitleInquiry_ToTUFromTUReconcilliation,None,True,,2024-08-09T16:06:42Z,2025-01-03T17:34:13Z,2025-01-03T17:34:10Z,3276,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Retail_CMSTitleInquiry_ToTUFromTUReconcilliation,https://github.com/cox-cei/RPA_CAI_Retail_CMSTitleInquiry_ToTUFromTUReconcilliation.git
RPA_CAI_Retail_EsntialAftermarket_Dispatcher,cox-cei/RPA_CAI_Retail_EsntialAftermarket_Dispatcher,None,True,HTML,2024-08-09T16:05:54Z,2024-10-18T23:10:12Z,2024-12-02T19:10:38Z,2315,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Retail_EsntialAftermarket_Dispatcher,https://github.com/cox-cei/RPA_CAI_Retail_EsntialAftermarket_Dispatcher.git
RPA_CAI_Retail_EsntialAftermarket_FIEPerformer,cox-cei/RPA_CAI_Retail_EsntialAftermarket_FIEPerformer,None,True,HTML,2024-08-09T16:06:02Z,2024-10-18T23:10:17Z,2024-11-22T23:10:24Z,5550,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Retail_EsntialAftermarket_FIEPerformer,https://github.com/cox-cei/RPA_CAI_Retail_EsntialAftermarket_FIEPerformer.git
RPA_CAI_Retail_EsntialAftermarket_Performer,cox-cei/RPA_CAI_Retail_EsntialAftermarket_Performer,None,True,HTML,2024-08-09T16:08:34Z,2024-10-18T23:10:47Z,2024-11-22T22:56:36Z,8017,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Retail_EsntialAftermarket_Performer,https://github.com/cox-cei/RPA_CAI_Retail_EsntialAftermarket_Performer.git
RPA_CAI_Retail_EsntialStrapi_RefreshRetailerTenants,cox-cei/RPA_CAI_Retail_EsntialStrapi_RefreshRetailerTenants,None,True,,2024-08-09T16:08:25Z,2024-10-18T23:10:54Z,2024-08-12T18:23:53Z,216,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Retail_EsntialStrapi_RefreshRetailerTenants,https://github.com/cox-cei/RPA_CAI_Retail_EsntialStrapi_RefreshRetailerTenants.git
RPA_CAI_Retail_EsntialStrapi_Reporting,cox-cei/RPA_CAI_Retail_EsntialStrapi_Reporting,None,True,HTML,2024-08-09T16:09:58Z,2024-10-28T16:58:42Z,2024-11-15T00:03:45Z,135,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Retail_EsntialStrapi_Reporting,https://github.com/cox-cei/RPA_CAI_Retail_EsntialStrapi_Reporting.git
RPA_CAI_Retail_EsntialStrapi_RetailerContentUpdatePerformer,cox-cei/RPA_CAI_Retail_EsntialStrapi_RetailerContentUpdatePerformer,None,True,,2024-08-09T16:06:44Z,2024-10-28T16:48:52Z,2024-11-15T00:05:08Z,3700,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Retail_EsntialStrapi_RetailerContentUpdatePerformer,https://github.com/cox-cei/RPA_CAI_Retail_EsntialStrapi_RetailerContentUpdatePerformer.git
RPA_CAI_Retail_EsntialStrapi_RetailerFileDispatcher,cox-cei/RPA_CAI_Retail_EsntialStrapi_RetailerFileDispatcher,None,True,,2024-08-09T16:05:20Z,2024-10-28T16:53:53Z,2024-10-28T16:54:24Z,1459,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_Retail_EsntialStrapi_RetailerFileDispatcher,https://github.com/cox-cei/RPA_CAI_Retail_EsntialStrapi_RetailerFileDispatcher.git
RPA_CAI_SupplyChain_InvOrderFullfillment_AcceptCases,cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_AcceptCases,None,True,HTML,2024-08-09T16:05:58Z,2024-10-18T23:12:13Z,2024-11-19T21:12:34Z,6082,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_AcceptCases,https://github.com/cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_AcceptCases.git
RPA_CAI_SupplyChain_InvOrderFullfillment_FastlaneLibraryUI,cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_FastlaneLibraryUI,None,True,,2024-08-09T16:10:45Z,2024-10-18T23:12:32Z,2024-11-19T21:13:14Z,124,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_FastlaneLibraryUI,https://github.com/cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_FastlaneLibraryUI.git
RPA_CAI_SupplyChain_InvOrderFullfillment_GetCaseData,cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_GetCaseData,None,True,HTML,2024-08-09T16:01:25Z,2024-10-18T23:12:57Z,2024-11-19T21:13:38Z,1456,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_GetCaseData,https://github.com/cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_GetCaseData.git
RPA_CAI_SupplyChain_InvOrderFullfillment_HomenetLibraryUI,cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_HomenetLibraryUI,None,True,,2024-08-09T16:00:19Z,2024-10-18T23:13:21Z,2024-11-19T21:13:52Z,674,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_HomenetLibraryUI,https://github.com/cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_HomenetLibraryUI.git
RPA_CAI_SupplyChain_InvOrderFullfillment_Performer,cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_Performer,None,True,HTML,2024-08-09T16:06:06Z,2024-10-18T23:13:42Z,2024-11-19T21:14:05Z,4973,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_Performer,https://github.com/cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_Performer.git
RPA_CAI_SupplyChain_InvOrderFullfillment_SalesforceLibraryUI,cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_SalesforceLibraryUI,None,True,,2024-08-09T16:01:23Z,2024-10-18T23:14:01Z,2024-11-19T21:14:17Z,878,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_SalesforceLibraryUI,https://github.com/cox-cei/RPA_CAI_SupplyChain_InvOrderFullfillment_SalesforceLibraryUI.git
RPA_CCI_Finance_APCoding_Dispatcher,cox-cei/RPA_CCI_Finance_APCoding_Dispatcher,None,True,,2024-08-09T16:04:20Z,2025-07-15T07:52:30Z,2025-07-15T07:52:27Z,1086,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_APCoding_Dispatcher,https://github.com/cox-cei/RPA_CCI_Finance_APCoding_Dispatcher.git
RPA_CCI_Finance_APCoding_Performer,cox-cei/RPA_CCI_Finance_APCoding_Performer,None,True,,2024-08-09T16:03:41Z,2025-07-17T13:28:32Z,2025-07-17T13:28:28Z,23403,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_APCoding_Performer,https://github.com/cox-cei/RPA_CCI_Finance_APCoding_Performer.git
RPA_CCI_Finance_APCoding_ReportGeneration,cox-cei/RPA_CCI_Finance_APCoding_ReportGeneration,None,True,,2024-08-09T16:05:12Z,2025-07-15T07:52:48Z,2025-07-15T07:52:45Z,1021,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_APCoding_ReportGeneration,https://github.com/cox-cei/RPA_CCI_Finance_APCoding_ReportGeneration.git
RPA_CCI_Finance_APCoding_SSOLogin,cox-cei/RPA_CCI_Finance_APCoding_SSOLogin,None,True,,2024-08-09T16:07:34Z,2024-10-18T23:15:12Z,2024-08-12T18:41:33Z,1692,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_APCoding_SSOLogin,https://github.com/cox-cei/RPA_CCI_Finance_APCoding_SSOLogin.git
RPA_CCI_Finance_APCoding_WebcenterObjects,cox-cei/RPA_CCI_Finance_APCoding_WebcenterObjects,None,True,,2024-08-09T16:01:55Z,2024-10-18T23:15:43Z,2024-08-12T18:41:28Z,1217,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_APCoding_WebcenterObjects,https://github.com/cox-cei/RPA_CCI_Finance_APCoding_WebcenterObjects.git
RPA_CCI_Finance_MEC_AP,cox-cei/RPA_CCI_Finance_MEC_AP,None,True,Rich Text Format,2024-08-09T16:05:56Z,2024-10-18T23:15:46Z,2024-08-12T19:03:16Z,3084,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_MEC_AP,https://github.com/cox-cei/RPA_CCI_Finance_MEC_AP.git
RPA_CCI_Finance_MEC_AR,cox-cei/RPA_CCI_Finance_MEC_AR,None,True,,2024-08-09T16:08:23Z,2024-10-18T23:16:18Z,2024-08-12T19:07:19Z,5076,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_MEC_AR,https://github.com/cox-cei/RPA_CCI_Finance_MEC_AR.git
RPA_CCI_Finance_MEC_BlacklineRecon,cox-cei/RPA_CCI_Finance_MEC_BlacklineRecon,None,True,HTML,2024-08-09T16:05:16Z,2025-04-21T20:20:49Z,2025-04-22T13:53:46Z,12409,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_MEC_BlacklineRecon,https://github.com/cox-cei/RPA_CCI_Finance_MEC_BlacklineRecon.git
RPA_CCI_Finance_MEC_CM,cox-cei/RPA_CCI_Finance_MEC_CM,None,True,,2024-08-09T16:04:24Z,2024-10-18T23:16:50Z,2024-08-12T19:07:37Z,16854,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_MEC_CM,https://github.com/cox-cei/RPA_CCI_Finance_MEC_CM.git
RPA_CCI_Finance_MEC_FA,cox-cei/RPA_CCI_Finance_MEC_FA,None,True,Rich Text Format,2024-08-09T16:06:08Z,2024-11-15T19:45:29Z,2025-01-20T18:54:42Z,7586,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_MEC_FA,https://github.com/cox-cei/RPA_CCI_Finance_MEC_FA.git
RPA_CCI_Finance_MEC_GL,cox-cei/RPA_CCI_Finance_MEC_GL,None,True,VBA,2024-08-09T16:03:00Z,2025-01-20T15:31:31Z,2025-01-20T15:31:30Z,52449,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_MEC_GL,https://github.com/cox-cei/RPA_CCI_Finance_MEC_GL.git
RPA_CCI_Finance_MEC_INV,cox-cei/RPA_CCI_Finance_MEC_INV,None,True,Rich Text Format,2024-08-09T16:05:07Z,2024-11-15T19:49:13Z,2024-11-15T19:49:07Z,50554,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_MEC_INV,https://github.com/cox-cei/RPA_CCI_Finance_MEC_INV.git
RPA_CCI_Finance_MEC_PA,cox-cei/RPA_CCI_Finance_MEC_PA,None,True,,2024-08-09T16:06:11Z,2024-11-15T19:50:42Z,2024-11-15T19:50:38Z,5342,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_MEC_PA,https://github.com/cox-cei/RPA_CCI_Finance_MEC_PA.git
RPA_CCI_Finance_MEC_PO,cox-cei/RPA_CCI_Finance_MEC_PO,None,True,Rich Text Format,2024-08-09T16:09:14Z,2025-01-17T14:59:51Z,2025-01-17T14:59:50Z,13042,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_MEC_PO,https://github.com/cox-cei/RPA_CCI_Finance_MEC_PO.git
RPA_CCI_Finance_ProactivePaymentsNotification_Dispatcher,cox-cei/RPA_CCI_Finance_ProactivePaymentsNotification_Dispatcher,None,True,,2024-08-09T16:06:46Z,2024-10-18T23:18:53Z,2024-08-12T18:40:56Z,1109,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_ProactivePaymentsNotification_Dispatcher,https://github.com/cox-cei/RPA_CCI_Finance_ProactivePaymentsNotification_Dispatcher.git
RPA_CCI_Finance_ProactivePaymentsNotification_Performer,cox-cei/RPA_CCI_Finance_ProactivePaymentsNotification_Performer,None,True,HTML,2024-08-09T16:10:40Z,2024-10-18T23:18:50Z,2024-08-12T18:41:00Z,1045,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_ProactivePaymentsNotification_Performer,https://github.com/cox-cei/RPA_CCI_Finance_ProactivePaymentsNotification_Performer.git
RPA_CCI_Finance_ProactivePaymentsNotification_Reporter,cox-cei/RPA_CCI_Finance_ProactivePaymentsNotification_Reporter,None,True,,2024-08-09T16:08:32Z,2024-10-18T23:19:31Z,2024-08-12T18:41:05Z,990,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Finance_ProactivePaymentsNotification_Reporter,https://github.com/cox-cei/RPA_CCI_Finance_ProactivePaymentsNotification_Reporter.git
RPA_CCI_HR_MinuteGap_Reporter,cox-cei/RPA_CCI_HR_MinuteGap_Reporter,None,True,HTML,2024-08-09T16:06:47Z,2024-10-18T23:19:33Z,2024-08-12T17:59:23Z,7156,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_HR_MinuteGap_Reporter,https://github.com/cox-cei/RPA_CCI_HR_MinuteGap_Reporter.git
RPA_CCI_HR_MinuteGap_Upload,cox-cei/RPA_CCI_HR_MinuteGap_Upload,None,True,,2024-08-09T16:10:54Z,2024-10-18T23:20:10Z,2024-08-12T17:59:28Z,1615,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_HR_MinuteGap_Upload,https://github.com/cox-cei/RPA_CCI_HR_MinuteGap_Upload.git
RPA_CCI_HR_MinuteGap_Worker,cox-cei/RPA_CCI_HR_MinuteGap_Worker,None,True,,2024-08-09T16:10:04Z,2024-10-18T23:20:17Z,2025-05-15T19:10:34Z,33870,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA_CCI_HR_MinuteGap_Worker,https://github.com/cox-cei/RPA_CCI_HR_MinuteGap_Worker.git
RPA_CCI_HR_NPTE_OFSCToWorkday,cox-cei/RPA_CCI_HR_NPTE_OFSCToWorkday,None,True,,2024-08-09T16:11:24Z,2024-10-18T23:20:53Z,2024-08-12T17:59:43Z,8302,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_HR_NPTE_OFSCToWorkday,https://github.com/cox-cei/RPA_CCI_HR_NPTE_OFSCToWorkday.git
RPA_CCI_HR_NPTE_Reporter,cox-cei/RPA_CCI_HR_NPTE_Reporter,None,True,HTML,2024-08-09T16:10:43Z,2024-10-18T23:20:59Z,2024-08-12T17:59:51Z,7176,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_HR_NPTE_Reporter,https://github.com/cox-cei/RPA_CCI_HR_NPTE_Reporter.git
RPA_CCI_HR_NPTE_Upload,cox-cei/RPA_CCI_HR_NPTE_Upload,None,True,,2024-08-09T16:09:57Z,2024-10-18T23:21:33Z,2024-08-12T18:03:15Z,17345,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_HR_NPTE_Upload,https://github.com/cox-cei/RPA_CCI_HR_NPTE_Upload.git
RPA_CCI_HR_NPTE_WorkdayToOFSC,cox-cei/RPA_CCI_HR_NPTE_WorkdayToOFSC,None,True,,2024-08-09T16:04:27Z,2024-10-18T23:21:39Z,2024-08-12T18:03:10Z,62205,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_HR_NPTE_WorkdayToOFSC,https://github.com/cox-cei/RPA_CCI_HR_NPTE_WorkdayToOFSC.git
RPA_CCI_Legal_IPSubpoena_Dispatcher,cox-cei/RPA_CCI_Legal_IPSubpoena_Dispatcher,None,True,,2024-08-09T16:04:33Z,2024-11-15T20:00:25Z,2025-04-23T13:25:17Z,13131,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Legal_IPSubpoena_Dispatcher,https://github.com/cox-cei/RPA_CCI_Legal_IPSubpoena_Dispatcher.git
RPA_CCI_Legal_IPSubpoena_Performer,cox-cei/RPA_CCI_Legal_IPSubpoena_Performer,None,True,,2024-08-09T16:05:22Z,2024-11-15T20:02:00Z,2025-03-30T20:10:29Z,23830,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Legal_IPSubpoena_Performer,https://github.com/cox-cei/RPA_CCI_Legal_IPSubpoena_Performer.git
RPA_CCI_Legal_IPSubpoena_Reporting,cox-cei/RPA_CCI_Legal_IPSubpoena_Reporting,None,True,HTML,2024-08-09T16:08:17Z,2024-11-15T20:03:35Z,2024-11-15T20:03:30Z,320,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Legal_IPSubpoena_Reporting,https://github.com/cox-cei/RPA_CCI_Legal_IPSubpoena_Reporting.git
RPA_CCI_Sourcing_PricingAssurance_MailLoader,cox-cei/RPA_CCI_Sourcing_PricingAssurance_MailLoader,None,True,,2024-08-09T16:10:02Z,2024-10-18T23:23:07Z,2025-07-28T13:48:07Z,5278,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Sourcing_PricingAssurance_MailLoader,https://github.com/cox-cei/RPA_CCI_Sourcing_PricingAssurance_MailLoader.git
RPA_CCI_Sourcing_PricingAssurance_OracleDescriptors,cox-cei/RPA_CCI_Sourcing_PricingAssurance_OracleDescriptors,None,True,C#,2024-08-09T16:02:43Z,2025-04-03T20:06:37Z,2025-04-03T20:06:33Z,4169,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Sourcing_PricingAssurance_OracleDescriptors,https://github.com/cox-cei/RPA_CCI_Sourcing_PricingAssurance_OracleDescriptors.git
RPA_CCI_Sourcing_PricingAssurance_TicketCreator,cox-cei/RPA_CCI_Sourcing_PricingAssurance_TicketCreator,None,True,HTML,2024-08-09T16:00:23Z,2025-07-29T14:03:10Z,2025-07-29T14:03:06Z,51102,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Sourcing_PricingAssurance_TicketCreator,https://github.com/cox-cei/RPA_CCI_Sourcing_PricingAssurance_TicketCreator.git
RPA_CCI_Sourcing_PricingAssurance_TicketLoader,cox-cei/RPA_CCI_Sourcing_PricingAssurance_TicketLoader,None,True,,2024-08-09T16:02:53Z,2025-01-21T14:02:06Z,2025-01-21T14:02:03Z,34137,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Sourcing_PricingAssurance_TicketLoader,https://github.com/cox-cei/RPA_CCI_Sourcing_PricingAssurance_TicketLoader.git
RPA_CCI_Sourcing_PricingAssurance_TicketWorker,cox-cei/RPA_CCI_Sourcing_PricingAssurance_TicketWorker,None,True,,2024-08-09T16:05:24Z,2025-03-06T17:40:35Z,2025-03-06T17:40:31Z,55539,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Sourcing_PricingAssurance_TicketWorker,https://github.com/cox-cei/RPA_CCI_Sourcing_PricingAssurance_TicketWorker.git
RPA_CCI_Sourcing_RMA_ARRISWorker,cox-cei/RPA_CCI_Sourcing_RMA_ARRISWorker,None,True,,2024-08-09T16:00:33Z,2025-07-28T15:30:15Z,2025-07-28T15:30:12Z,1984,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Sourcing_RMA_ARRISWorker,https://github.com/cox-cei/RPA_CCI_Sourcing_RMA_ARRISWorker.git
RPA_CCI_Sourcing_RMA_CISCOWorker,cox-cei/RPA_CCI_Sourcing_RMA_CISCOWorker,None,True,C#,2024-08-09T16:10:51Z,2025-07-29T14:17:29Z,2025-07-29T14:17:26Z,4356,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Sourcing_RMA_CISCOWorker,https://github.com/cox-cei/RPA_CCI_Sourcing_RMA_CISCOWorker.git
RPA_CCI_Sourcing_RMA_EmailWorker,cox-cei/RPA_CCI_Sourcing_RMA_EmailWorker,None,True,,2024-08-09T16:03:35Z,2025-07-28T15:30:21Z,2025-07-28T15:30:18Z,1966,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA_CCI_Sourcing_RMA_EmailWorker,https://github.com/cox-cei/RPA_CCI_Sourcing_RMA_EmailWorker.git
RPA_CCI_Sourcing_RMA_Error_Reporting,cox-cei/RPA_CCI_Sourcing_RMA_Error_Reporting,Generates an excel report of the previous day's RMA errors and sends an email to the business owner for review.,True,HTML,2025-07-29T12:33:06Z,2025-07-29T18:25:34Z,2025-08-01T13:14:32Z,1124,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Sourcing_RMA_Error_Reporting,https://github.com/cox-cei/RPA_CCI_Sourcing_RMA_Error_Reporting.git
RPA_CCI_Sourcing_RMA_RMADescriptors,cox-cei/RPA_CCI_Sourcing_RMA_RMADescriptors,None,True,C#,2024-08-09T16:02:58Z,2025-07-25T18:47:44Z,2025-08-01T13:15:27Z,19517,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Sourcing_RMA_RMADescriptors,https://github.com/cox-cei/RPA_CCI_Sourcing_RMA_RMADescriptors.git
RPA_CCI_Sourcing_RMA_Upload,cox-cei/RPA_CCI_Sourcing_RMA_Upload,None,True,,2024-08-09T16:07:44Z,2025-07-10T17:28:06Z,2025-08-01T19:25:13Z,16857,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_Sourcing_RMA_Upload,https://github.com/cox-cei/RPA_CCI_Sourcing_RMA_Upload.git
RPA_CCI_SupplyChain_ETOS_Dispatcher,cox-cei/RPA_CCI_SupplyChain_ETOS_Dispatcher,None,True,,2024-08-09T16:01:12Z,2025-06-23T15:07:49Z,2025-06-23T15:07:46Z,1442,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_SupplyChain_ETOS_Dispatcher,https://github.com/cox-cei/RPA_CCI_SupplyChain_ETOS_Dispatcher.git
RPA_CCI_SupplyChain_ETOS_Objects,cox-cei/RPA_CCI_SupplyChain_ETOS_Objects,None,True,,2024-08-09T16:06:49Z,2024-10-18T23:26:48Z,2024-08-12T18:45:17Z,2225,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_SupplyChain_ETOS_Objects,https://github.com/cox-cei/RPA_CCI_SupplyChain_ETOS_Objects.git
RPA_CCI_SupplyChain_ETOS_Performer,cox-cei/RPA_CCI_SupplyChain_ETOS_Performer,None,True,,2024-08-09T16:05:11Z,2025-03-18T17:04:25Z,2025-05-26T08:21:54Z,28598,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CCI_SupplyChain_ETOS_Performer,https://github.com/cox-cei/RPA_CCI_SupplyChain_ETOS_Performer.git
RPA_CEI_CSC_CallSummarization_GenesysDispatcher,cox-cei/RPA_CEI_CSC_CallSummarization_GenesysDispatcher,None,True,,2024-08-09T16:02:05Z,2024-10-18T23:27:24Z,2024-10-24T16:31:07Z,480,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_CSC_CallSummarization_GenesysDispatcher,https://github.com/cox-cei/RPA_CEI_CSC_CallSummarization_GenesysDispatcher.git
RPA_CEI_CSC_CallSummarization_Performer,cox-cei/RPA_CEI_CSC_CallSummarization_Performer,None,True,,2024-08-09T16:00:37Z,2025-04-10T16:58:55Z,2025-04-10T16:58:53Z,2159,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_CSC_CallSummarization_Performer,https://github.com/cox-cei/RPA_CEI_CSC_CallSummarization_Performer.git
RPA_CEI_CSC_CallSummarization_TranscriptionPerformer,cox-cei/RPA_CEI_CSC_CallSummarization_TranscriptionPerformer,None,True,,2024-08-09T16:06:04Z,2024-10-18T23:28:03Z,2024-10-24T16:36:15Z,689,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_CSC_CallSummarization_TranscriptionPerformer,https://github.com/cox-cei/RPA_CEI_CSC_CallSummarization_TranscriptionPerformer.git
RPA_CEI_CSC_CallSummarization_WeeklyReporter,cox-cei/RPA_CEI_CSC_CallSummarization_WeeklyReporter,None,True,,2024-08-09T16:06:57Z,2024-10-18T23:28:24Z,2024-10-24T16:37:34Z,371,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_CSC_CallSummarization_WeeklyReporter,https://github.com/cox-cei/RPA_CEI_CSC_CallSummarization_WeeklyReporter.git
RPA_CEI_Finance_EnhancedInvoicePull_Dispatcher,cox-cei/RPA_CEI_Finance_EnhancedInvoicePull_Dispatcher,None,True,,2024-08-09T16:00:31Z,2024-10-18T23:28:47Z,2024-08-12T18:45:32Z,985,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Finance_EnhancedInvoicePull_Dispatcher,https://github.com/cox-cei/RPA_CEI_Finance_EnhancedInvoicePull_Dispatcher.git
RPA_CEI_Finance_EnhancedInvoicePull_EBSWebSSOLogin,cox-cei/RPA_CEI_Finance_EnhancedInvoicePull_EBSWebSSOLogin,None,True,,2024-08-09T16:00:21Z,2024-10-18T23:29:05Z,2024-08-12T18:45:35Z,2516,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Finance_EnhancedInvoicePull_EBSWebSSOLogin,https://github.com/cox-cei/RPA_CEI_Finance_EnhancedInvoicePull_EBSWebSSOLogin.git
RPA_CEI_Finance_EnhancedInvoicePull_Objects,cox-cei/RPA_CEI_Finance_EnhancedInvoicePull_Objects,None,True,,2024-08-09T16:10:00Z,2025-02-18T07:48:45Z,2025-02-18T07:48:42Z,1572,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Finance_EnhancedInvoicePull_Objects,https://github.com/cox-cei/RPA_CEI_Finance_EnhancedInvoicePull_Objects.git
RPA_CEI_Finance_EnhancedInvoicePull_Performer,cox-cei/RPA_CEI_Finance_EnhancedInvoicePull_Performer,None,True,,2024-08-09T16:08:21Z,2025-02-18T07:48:04Z,2025-04-16T21:10:13Z,4564,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Finance_EnhancedInvoicePull_Performer,https://github.com/cox-cei/RPA_CEI_Finance_EnhancedInvoicePull_Performer.git
RPA_CEI_Finance_MEC_BitBucketTest,cox-cei/RPA_CEI_Finance_MEC_BitBucketTest,None,True,Rich Text Format,2024-08-09T16:03:33Z,2024-10-18T23:30:12Z,2024-08-12T19:07:23Z,1139,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Finance_MEC_BitBucketTest,https://github.com/cox-cei/RPA_CEI_Finance_MEC_BitBucketTest.git
RPA_CEI_Finance_MEC_SendEmail,cox-cei/RPA_CEI_Finance_MEC_SendEmail,None,True,,2024-08-09T16:01:59Z,2024-10-18T23:30:29Z,2024-11-08T11:31:34Z,141,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Finance_MEC_SendEmail,https://github.com/cox-cei/RPA_CEI_Finance_MEC_SendEmail.git
RPA_CEI_Finance_MEC_Sharepoint,cox-cei/RPA_CEI_Finance_MEC_Sharepoint,None,True,,2024-08-09T16:04:22Z,2024-10-18T23:30:53Z,2024-11-08T11:30:59Z,172,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Finance_MEC_Sharepoint,https://github.com/cox-cei/RPA_CEI_Finance_MEC_Sharepoint.git
RPA_CEI_Finance_MEC_SmartSheet,cox-cei/RPA_CEI_Finance_MEC_SmartSheet,None,True,,2024-08-09T16:04:25Z,2024-10-18T23:31:11Z,2024-08-12T19:12:34Z,9,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Finance_MEC_SmartSheet,https://github.com/cox-cei/RPA_CEI_Finance_MEC_SmartSheet.git
RPA_CEI_Finance_MEC_Snow,cox-cei/RPA_CEI_Finance_MEC_Snow,None,True,,2024-08-09T16:07:38Z,2024-10-18T23:31:35Z,2024-08-12T19:12:38Z,9,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Finance_MEC_Snow,https://github.com/cox-cei/RPA_CEI_Finance_MEC_Snow.git
RPA_CEI_Finance_MEC_Testing,cox-cei/RPA_CEI_Finance_MEC_Testing,None,True,,2024-08-09T16:06:09Z,2024-10-18T23:31:53Z,2024-08-12T19:12:48Z,221,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Finance_MEC_Testing,https://github.com/cox-cei/RPA_CEI_Finance_MEC_Testing.git
RPA_CEI_HR_OnboardingCommunications_BatchHandler,cox-cei/RPA_CEI_HR_OnboardingCommunications_BatchHandler,None,True,,2024-08-09T16:02:51Z,2024-10-18T23:32:17Z,2024-12-19T17:21:18Z,5986,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_HR_OnboardingCommunications_BatchHandler,https://github.com/cox-cei/RPA_CEI_HR_OnboardingCommunications_BatchHandler.git
RPA_CEI_HR_OnboardingCommunications_EmailHandler,cox-cei/RPA_CEI_HR_OnboardingCommunications_EmailHandler,None,True,,2024-08-09T16:03:43Z,2025-07-21T17:23:11Z,2025-07-21T17:23:08Z,2851,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_HR_OnboardingCommunications_EmailHandler,https://github.com/cox-cei/RPA_CEI_HR_OnboardingCommunications_EmailHandler.git
RPA_CEI_HR_OnboardingCommunications_Loader,cox-cei/RPA_CEI_HR_OnboardingCommunications_Loader,None,True,,2024-08-09T16:03:45Z,2024-10-18T23:32:56Z,2025-05-19T18:47:37Z,2408,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_HR_OnboardingCommunications_Loader,https://github.com/cox-cei/RPA_CEI_HR_OnboardingCommunications_Loader.git
RPA_CEI_HR_OnboardingCommunications_Reporter,cox-cei/RPA_CEI_HR_OnboardingCommunications_Reporter,None,True,,2024-08-09T16:05:09Z,2024-12-19T17:52:24Z,2024-12-19T17:52:20Z,2306,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_HR_OnboardingCommunications_Reporter,https://github.com/cox-cei/RPA_CEI_HR_OnboardingCommunications_Reporter.git
RPA_CEI_HR_TimeAdjustment_WokrdayTimeAdjustmentToEBS,cox-cei/RPA_CEI_HR_TimeAdjustment_WokrdayTimeAdjustmentToEBS,None,True,,2024-08-09T16:10:08Z,2024-11-12T14:17:09Z,2024-11-14T15:24:53Z,11737,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA_CEI_HR_TimeAdjustment_WokrdayTimeAdjustmentToEBS,https://github.com/cox-cei/RPA_CEI_HR_TimeAdjustment_WokrdayTimeAdjustmentToEBS.git
RPA_CEI_IA_Internal_Encryption,cox-cei/RPA_CEI_IA_Internal_Encryption,None,True,C#,2024-08-09T16:09:53Z,2025-03-14T13:18:35Z,2025-03-14T13:18:32Z,30,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_IA_Internal_Encryption,https://github.com/cox-cei/RPA_CEI_IA_Internal_Encryption.git
RPA_CEI_IA_Internal_Reporting,cox-cei/RPA_CEI_IA_Internal_Reporting,None,True,,2024-08-09T16:09:19Z,2024-10-18T23:34:19Z,2024-08-12T19:12:57Z,13,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_IA_Internal_Reporting,https://github.com/cox-cei/RPA_CEI_IA_Internal_Reporting.git
RPA_CEI_IA_Internal_ReportingTemplate,cox-cei/RPA_CEI_IA_Internal_ReportingTemplate,None,True,,2024-08-09T16:36:03Z,2024-10-19T00:40:47Z,2024-10-23T18:19:10Z,155,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_IA_Internal_ReportingTemplate,https://github.com/cox-cei/RPA_CEI_IA_Internal_ReportingTemplate.git
RPA_CEI_IA_QueueReporter_QueueReporter,cox-cei/RPA_CEI_IA_QueueReporter_QueueReporter,None,True,,2024-08-09T16:00:27Z,2025-07-08T18:07:44Z,2025-08-01T20:19:23Z,322,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_IA_QueueReporter_QueueReporter,https://github.com/cox-cei/RPA_CEI_IA_QueueReporter_QueueReporter.git
RPA_CEI_IBT_ContractorSetup_OraclePerformer,cox-cei/RPA_CEI_IBT_ContractorSetup_OraclePerformer,None,True,HTML,2024-08-09T16:06:53Z,2025-01-06T19:49:43Z,2025-01-06T19:49:41Z,3268,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_IBT_ContractorSetup_OraclePerformer,https://github.com/cox-cei/RPA_CEI_IBT_ContractorSetup_OraclePerformer.git
RPA_CEI_IBT_ContractorSetup_SafeSendDispatcher,cox-cei/RPA_CEI_IBT_ContractorSetup_SafeSendDispatcher,None,True,HTML,2024-08-09T16:10:06Z,2025-06-17T14:27:41Z,2025-06-17T14:27:35Z,6011,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_IBT_ContractorSetup_SafeSendDispatcher,https://github.com/cox-cei/RPA_CEI_IBT_ContractorSetup_SafeSendDispatcher.git
RPA_CEI_IBT_ContractorSetup_SmartSheetPerformer,cox-cei/RPA_CEI_IBT_ContractorSetup_SmartSheetPerformer,None,True,HTML,2024-08-09T16:10:38Z,2024-12-05T15:47:20Z,2025-05-16T18:40:30Z,3013,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_IBT_ContractorSetup_SmartSheetPerformer,https://github.com/cox-cei/RPA_CEI_IBT_ContractorSetup_SmartSheetPerformer.git
RPA_CEI_IBT_TableauLicenseMgmt_ACFormHandler,cox-cei/RPA_CEI_IBT_TableauLicenseMgmt_ACFormHandler,None,True,,2024-08-09T16:07:42Z,2025-01-06T19:51:36Z,2025-01-06T19:51:34Z,33,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_IBT_TableauLicenseMgmt_ACFormHandler,https://github.com/cox-cei/RPA_CEI_IBT_TableauLicenseMgmt_ACFormHandler.git
RPA_CEI_IBT_TableauLicenseMgmt_LicenseRetrievalRequest,cox-cei/RPA_CEI_IBT_TableauLicenseMgmt_LicenseRetrievalRequest,None,True,HTML,2024-08-09T16:06:59Z,2025-04-16T18:57:49Z,2025-04-16T18:57:46Z,607,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_IBT_TableauLicenseMgmt_LicenseRetrievalRequest,https://github.com/cox-cei/RPA_CEI_IBT_TableauLicenseMgmt_LicenseRetrievalRequest.git
RPA_CEI_IBT_TableauLicenseMgmt_NewLicenseRequest,cox-cei/RPA_CEI_IBT_TableauLicenseMgmt_NewLicenseRequest,None,True,HTML,2024-08-09T16:10:52Z,2025-01-06T19:50:50Z,2025-01-06T19:50:47Z,668,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_IBT_TableauLicenseMgmt_NewLicenseRequest,https://github.com/cox-cei/RPA_CEI_IBT_TableauLicenseMgmt_NewLicenseRequest.git
RPA_CEI_IBT_TableauLicenseMgmt_RequestDispatcher,cox-cei/RPA_CEI_IBT_TableauLicenseMgmt_RequestDispatcher,None,True,HTML,2024-08-09T16:01:19Z,2025-01-06T19:50:19Z,2025-01-06T19:50:17Z,590,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_IBT_TableauLicenseMgmt_RequestDispatcher,https://github.com/cox-cei/RPA_CEI_IBT_TableauLicenseMgmt_RequestDispatcher.git
RPA_CEI_IBT_TelecomBillingInventory_EDIDispatcher,cox-cei/RPA_CEI_IBT_TelecomBillingInventory_EDIDispatcher,None,True,HTML,2024-08-09T16:01:21Z,2024-10-18T23:37:37Z,2024-08-12T18:52:37Z,539,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_IBT_TelecomBillingInventory_EDIDispatcher,https://github.com/cox-cei/RPA_CEI_IBT_TelecomBillingInventory_EDIDispatcher.git
RPA_CEI_IBT_TelecomBillingInventory_EDIParser,cox-cei/RPA_CEI_IBT_TelecomBillingInventory_EDIParser,None,True,HTML,2024-08-09T16:01:08Z,2024-10-18T23:38:12Z,2024-08-12T18:52:41Z,557,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_IBT_TelecomBillingInventory_EDIParser,https://github.com/cox-cei/RPA_CEI_IBT_TelecomBillingInventory_EDIParser.git
RPA_CEI_IBT_TelecomBillingInventory_EDIReporter,cox-cei/RPA_CEI_IBT_TelecomBillingInventory_EDIReporter,None,True,HTML,2024-08-09T16:04:36Z,2024-10-18T23:38:20Z,2024-08-12T18:52:45Z,556,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_IBT_TelecomBillingInventory_EDIReporter,https://github.com/cox-cei/RPA_CEI_IBT_TelecomBillingInventory_EDIReporter.git
RPA_CEI_Legal_CounselLink_LoginToCounselLink,cox-cei/RPA_CEI_Legal_CounselLink_LoginToCounselLink,None,True,,2024-08-09T16:04:35Z,2025-06-12T16:36:03Z,2025-06-12T16:36:00Z,7760,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Legal_CounselLink_LoginToCounselLink,https://github.com/cox-cei/RPA_CEI_Legal_CounselLink_LoginToCounselLink.git
RPA_CEI_Legal_CounselLink_ReportDownloadObjects,cox-cei/RPA_CEI_Legal_CounselLink_ReportDownloadObjects,None,True,,2024-08-09T16:02:55Z,2024-10-18T23:38:57Z,2024-08-12T18:52:54Z,169,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Legal_CounselLink_ReportDownloadObjects,https://github.com/cox-cei/RPA_CEI_Legal_CounselLink_ReportDownloadObjects.git
RPA_CEI_Legal_CounselLink_ReportNavigation,cox-cei/RPA_CEI_Legal_CounselLink_ReportNavigation,None,True,,2024-08-09T16:02:45Z,2024-10-18T23:39:33Z,2024-08-12T18:52:59Z,384,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Legal_CounselLink_ReportNavigation,https://github.com/cox-cei/RPA_CEI_Legal_CounselLink_ReportNavigation.git
RPA_CEI_Legal_Phishing_PhishingMonitoringProcess,cox-cei/RPA_CEI_Legal_Phishing_PhishingMonitoringProcess,None,True,,2024-08-09T16:02:12Z,2024-10-18T23:39:36Z,2024-08-12T18:45:28Z,3649,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Legal_Phishing_PhishingMonitoringProcess,https://github.com/cox-cei/RPA_CEI_Legal_Phishing_PhishingMonitoringProcess.git
RPA_CEI_Legal_Reporting_unattended,cox-cei/RPA_CEI_Legal_Reporting_unattended,None,True,,2024-08-09T16:09:12Z,2025-07-01T17:38:59Z,2025-07-15T17:21:33Z,13613,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Legal_Reporting_unattended,https://github.com/cox-cei/RPA_CEI_Legal_Reporting_unattended.git
RPA_CEI_Legal_SubpoenaIntake_LoadQueue,cox-cei/RPA_CEI_Legal_SubpoenaIntake_LoadQueue,None,True,,2024-08-09T16:09:05Z,2025-05-28T12:06:49Z,2025-07-31T15:40:36Z,34954,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Legal_SubpoenaIntake_LoadQueue,https://github.com/cox-cei/RPA_CEI_Legal_SubpoenaIntake_LoadQueue.git
RPA_CEI_Legal_SubpoenaIntake_Performer,cox-cei/RPA_CEI_Legal_SubpoenaIntake_Performer,None,True,,2024-08-09T16:01:13Z,2025-05-28T12:03:03Z,2025-07-31T15:28:03Z,139847,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Legal_SubpoenaIntake_Performer,https://github.com/cox-cei/RPA_CEI_Legal_SubpoenaIntake_Performer.git
RPA_CEI_Legal_SubpoenaIntake_Reporting,cox-cei/RPA_CEI_Legal_SubpoenaIntake_Reporting,None,True,HTML,2024-08-09T16:09:08Z,2025-01-24T16:26:41Z,2025-01-24T16:26:37Z,851,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Legal_SubpoenaIntake_Reporting,https://github.com/cox-cei/RPA_CEI_Legal_SubpoenaIntake_Reporting.git
RPA_CEI_Legal_TableauDataRefresh_Dispatcher,cox-cei/RPA_CEI_Legal_TableauDataRefresh_Dispatcher,None,True,,2024-08-09T16:00:35Z,2024-10-18T23:41:23Z,2025-05-15T19:27:50Z,996,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Legal_TableauDataRefresh_Dispatcher,https://github.com/cox-cei/RPA_CEI_Legal_TableauDataRefresh_Dispatcher.git
RPA_CEI_Legal_TableauDataRefresh_Performer,cox-cei/RPA_CEI_Legal_TableauDataRefresh_Performer,None,True,,2024-08-09T16:05:14Z,2025-06-26T14:12:00Z,2025-06-26T14:11:57Z,7924,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Legal_TableauDataRefresh_Performer,https://github.com/cox-cei/RPA_CEI_Legal_TableauDataRefresh_Performer.git
RPA_CEI_MonitorTestCases_Performer,cox-cei/RPA_CEI_MonitorTestCases_Performer,,True,HTML,2025-04-15T14:30:22Z,2025-04-17T20:06:27Z,2025-04-17T20:06:24Z,1028,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_MonitorTestCases_Performer,https://github.com/cox-cei/RPA_CEI_MonitorTestCases_Performer.git
RPA_CEI_OrchestratorAPI,cox-cei/RPA_CEI_OrchestratorAPI,,True,,2025-04-15T14:34:07Z,2025-04-15T16:20:33Z,2025-04-17T18:35:14Z,982,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA_CEI_OrchestratorAPI,https://github.com/cox-cei/RPA_CEI_OrchestratorAPI.git
RPA_CEI_Retail_Strapi_Descriptors,cox-cei/RPA_CEI_Retail_Strapi_Descriptors,None,True,,2024-08-09T16:09:55Z,2024-10-18T23:42:04Z,2024-08-12T18:12:51Z,405,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Retail_Strapi_Descriptors,https://github.com/cox-cei/RPA_CEI_Retail_Strapi_Descriptors.git
RPA_CEI_Risk_RiskManagement_FireCope,cox-cei/RPA_CEI_Risk_RiskManagement_FireCope,None,True,VBA,2024-08-09T16:02:49Z,2024-10-18T23:42:10Z,2024-11-13T21:12:51Z,62,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Risk_RiskManagement_FireCope,https://github.com/cox-cei/RPA_CEI_Risk_RiskManagement_FireCope.git
RPA_CEI_Risk_RiskManagement_FireFacility,cox-cei/RPA_CEI_Risk_RiskManagement_FireFacility,None,True,VBA,2024-08-09T16:06:51Z,2024-10-18T23:42:43Z,2024-11-13T21:13:50Z,61,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Risk_RiskManagement_FireFacility,https://github.com/cox-cei/RPA_CEI_Risk_RiskManagement_FireFacility.git
RPA_CEI_Risk_RiskManagement_FireInspectionStatus,cox-cei/RPA_CEI_Risk_RiskManagement_FireInspectionStatus,None,True,VBA,2024-08-09T16:10:41Z,2024-10-18T23:42:49Z,2024-10-02T16:53:08Z,144,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Risk_RiskManagement_FireInspectionStatus,https://github.com/cox-cei/RPA_CEI_Risk_RiskManagement_FireInspectionStatus.git
RPA_CEI_Risk_RiskManagement_FireRecommendation,cox-cei/RPA_CEI_Risk_RiskManagement_FireRecommendation,None,True,VBA,2024-08-09T16:10:47Z,2024-10-18T23:43:20Z,2024-11-13T21:30:16Z,48,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Risk_RiskManagement_FireRecommendation,https://github.com/cox-cei/RPA_CEI_Risk_RiskManagement_FireRecommendation.git
RPA_CEI_SupplyChain_CoxMobileOrders_Performer,cox-cei/RPA_CEI_SupplyChain_CoxMobileOrders_Performer,None,True,,2024-08-09T16:01:10Z,2024-10-18T23:43:23Z,2024-08-12T19:03:27Z,784,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_SupplyChain_CoxMobileOrders_Performer,https://github.com/cox-cei/RPA_CEI_SupplyChain_CoxMobileOrders_Performer.git
RPA_CEI_Tax_StateApportionment_Dispatcher,cox-cei/RPA_CEI_Tax_StateApportionment_Dispatcher,None,True,,2024-08-09T16:07:32Z,2025-06-17T14:38:24Z,2025-06-17T14:38:19Z,1863,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Tax_StateApportionment_Dispatcher,https://github.com/cox-cei/RPA_CEI_Tax_StateApportionment_Dispatcher.git
RPA_CEI_Tax_StateApportionment_OneSourceLibrary,cox-cei/RPA_CEI_Tax_StateApportionment_OneSourceLibrary,,True,,2025-01-29T20:56:17Z,2025-07-09T12:47:57Z,2025-07-28T17:00:55Z,25536,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA_CEI_Tax_StateApportionment_OneSourceLibrary,https://github.com/cox-cei/RPA_CEI_Tax_StateApportionment_OneSourceLibrary.git
RPA_CEI_Tax_StateApportionment_Performer,cox-cei/RPA_CEI_Tax_StateApportionment_Performer,None,True,HTML,2024-08-09T16:08:28Z,2025-03-12T16:34:22Z,2025-07-02T17:39:43Z,57182,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Tax_StateApportionment_Performer,https://github.com/cox-cei/RPA_CEI_Tax_StateApportionment_Performer.git
RPA_CEI_Tax_StateApportionment_Reporting,cox-cei/RPA_CEI_Tax_StateApportionment_Reporting,None,True,HTML,2024-08-09T16:09:10Z,2024-10-18T23:44:35Z,2024-11-14T08:51:51Z,3623,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Tax_StateApportionment_Reporting,https://github.com/cox-cei/RPA_CEI_Tax_StateApportionment_Reporting.git
RPA_CEI_Treasury_CADCashReporting_Dispatcher,cox-cei/RPA_CEI_Treasury_CADCashReporting_Dispatcher,None,True,HTML,2024-09-13T16:28:41Z,2024-11-14T16:24:27Z,2024-11-14T16:24:25Z,992,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Treasury_CADCashReporting_Dispatcher,https://github.com/cox-cei/RPA_CEI_Treasury_CADCashReporting_Dispatcher.git
RPA_CEI_Treasury_CADCashReporting_Performer,cox-cei/RPA_CEI_Treasury_CADCashReporting_Performer,None,True,HTML,2024-09-13T16:28:43Z,2025-06-02T20:38:31Z,2025-06-02T20:38:26Z,96890,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Treasury_CADCashReporting_Performer,https://github.com/cox-cei/RPA_CEI_Treasury_CADCashReporting_Performer.git
RPA_CEI_Treasury_CashPositioning_Dispatcher,cox-cei/RPA_CEI_Treasury_CashPositioning_Dispatcher,None,True,,2024-08-09T16:03:37Z,2024-10-18T23:44:35Z,2024-08-12T19:02:55Z,946,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Treasury_CashPositioning_Dispatcher,https://github.com/cox-cei/RPA_CEI_Treasury_CashPositioning_Dispatcher.git
RPA_CEI_Treasury_CashPositioning_Objects,cox-cei/RPA_CEI_Treasury_CashPositioning_Objects,None,True,,2024-08-09T16:04:29Z,2025-05-28T11:58:22Z,2025-05-28T11:58:19Z,4195,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA_CEI_Treasury_CashPositioning_Objects,https://github.com/cox-cei/RPA_CEI_Treasury_CashPositioning_Objects.git
RPA_CEI_Treasury_CashPositioning_Performer,cox-cei/RPA_CEI_Treasury_CashPositioning_Performer,None,True,,2024-08-09T16:02:01Z,2025-05-28T12:09:37Z,2025-05-28T12:09:33Z,10542,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Treasury_CashPositioning_Performer,https://github.com/cox-cei/RPA_CEI_Treasury_CashPositioning_Performer.git
RPA_CEI_Treasury_CashReporting_Dispatcher,cox-cei/RPA_CEI_Treasury_CashReporting_Dispatcher,None,True,,2024-08-09T16:07:36Z,2024-10-18T23:45:48Z,2024-08-12T19:03:08Z,955,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Treasury_CashReporting_Dispatcher,https://github.com/cox-cei/RPA_CEI_Treasury_CashReporting_Dispatcher.git
RPA_CEI_Treasury_CashReporting_Performer,cox-cei/RPA_CEI_Treasury_CashReporting_Performer,None,True,,2024-08-09T16:03:39Z,2025-06-23T15:07:03Z,2025-06-23T15:07:00Z,7055,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CEI_Treasury_CashReporting_Performer,https://github.com/cox-cei/RPA_CEI_Treasury_CashReporting_Performer.git
RPA_CMG_Finance_MEC_AP,cox-cei/RPA_CMG_Finance_MEC_AP,None,True,CSS,2024-08-09T16:07:29Z,2024-10-18T23:46:26Z,2024-08-12T19:07:13Z,3773,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CMG_Finance_MEC_AP,https://github.com/cox-cei/RPA_CMG_Finance_MEC_AP.git
RPA_CMG_Finance_MEC_FA,cox-cei/RPA_CMG_Finance_MEC_FA,None,True,Rich Text Format,2024-08-09T16:02:57Z,2024-10-18T23:46:34Z,2024-11-08T08:05:49Z,5616,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CMG_Finance_MEC_FA,https://github.com/cox-cei/RPA_CMG_Finance_MEC_FA.git
RPA_CMG_Finance_MEC_GL,cox-cei/RPA_CMG_Finance_MEC_GL,None,True,Rich Text Format,2024-08-09T16:03:49Z,2024-10-18T23:47:04Z,2024-11-08T08:05:08Z,2937,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CMG_Finance_MEC_GL,https://github.com/cox-cei/RPA_CMG_Finance_MEC_GL.git
RPA_CMG_Finance_MEC_INV,cox-cei/RPA_CMG_Finance_MEC_INV,None,True,HTML,2024-08-09T16:09:21Z,2024-12-23T17:57:39Z,2024-12-23T17:57:36Z,11642,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CMG_Finance_MEC_INV,https://github.com/cox-cei/RPA_CMG_Finance_MEC_INV.git
RPA_CMG_Finance_MEC_PA,cox-cei/RPA_CMG_Finance_MEC_PA,None,True,,2024-08-09T16:07:40Z,2024-10-18T23:47:46Z,2024-11-08T08:05:42Z,5942,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CMG_Finance_MEC_PA,https://github.com/cox-cei/RPA_CMG_Finance_MEC_PA.git
RPA_CMG_Finance_MEC_PO,cox-cei/RPA_CMG_Finance_MEC_PO,None,True,Rich Text Format,2024-08-09T16:05:18Z,2024-11-19T15:46:32Z,2024-11-19T15:46:20Z,4388,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA_CMG_Finance_MEC_PO,https://github.com/cox-cei/RPA_CMG_Finance_MEC_PO.git
sample-app-aoai-chatGPT,cox-cei/sample-app-aoai-chatGPT,None,True,Python,2023-08-09T16:37:21Z,2024-10-18T20:20:33Z,2023-11-06T07:43:53Z,9814,0,0,0,5,main,False,False,https://github.com/cox-cei/sample-app-aoai-chatGPT,https://github.com/cox-cei/sample-app-aoai-chatGPT.git
season-for-sharing-deprecated,cox-cei/season-for-sharing-deprecated,None,True,HTML,2023-07-12T15:41:14Z,2024-10-18T20:20:46Z,2023-07-12T15:47:16Z,1967,0,0,0,0,master,False,False,https://github.com/cox-cei/season-for-sharing-deprecated,https://github.com/cox-cei/season-for-sharing-deprecated.git
server-to-sharepoint-sync,cox-cei/server-to-sharepoint-sync,This repo is to copy the file from linux server to sharepoint,True,,2025-05-29T16:15:47Z,2025-05-29T17:03:41Z,2025-05-29T17:03:38Z,4,0,0,0,0,develop,False,False,https://github.com/cox-cei/server-to-sharepoint-sync,https://github.com/cox-cei/server-to-sharepoint-sync.git
ServiceNow-Neoload-PerformanceTesting,cox-cei/ServiceNow-Neoload-PerformanceTesting,,True,,2024-11-18T19:09:02Z,2024-11-18T19:09:04Z,2024-11-18T19:09:03Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/ServiceNow-Neoload-PerformanceTesting,https://github.com/cox-cei/ServiceNow-Neoload-PerformanceTesting.git
SharePoint-CoxIntranet-WebParts,cox-cei/SharePoint-CoxIntranet-WebParts,None,True,TypeScript,2024-08-27T19:24:49Z,2025-02-12T19:13:13Z,2025-07-17T18:07:31Z,41610,1,1,0,2,main,False,False,https://github.com/cox-cei/SharePoint-CoxIntranet-WebParts,https://github.com/cox-cei/SharePoint-CoxIntranet-WebParts.git
sigma-access-policy-service,cox-cei/sigma-access-policy-service,,True,Go,2024-12-03T18:35:38Z,2024-12-03T19:07:01Z,2024-12-03T19:07:49Z,887,0,0,0,0,dev,False,False,https://github.com/cox-cei/sigma-access-policy-service,https://github.com/cox-cei/sigma-access-policy-service.git
sigma-amazonmq,cox-cei/sigma-amazonmq,,True,,2024-12-03T18:35:25Z,2024-12-03T18:35:27Z,2024-12-03T18:35:26Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/sigma-amazonmq,https://github.com/cox-cei/sigma-amazonmq.git
sigma-app-boilerplate,cox-cei/sigma-app-boilerplate,,True,,2024-12-03T18:35:09Z,2024-12-03T18:35:11Z,2024-12-03T18:35:09Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/sigma-app-boilerplate,https://github.com/cox-cei/sigma-app-boilerplate.git
sigma-vm-images,cox-cei/sigma-vm-images,,True,C#,2024-11-26T14:00:55Z,2024-12-03T18:03:52Z,2024-12-03T18:03:46Z,9,0,0,0,0,master,False,False,https://github.com/cox-cei/sigma-vm-images,https://github.com/cox-cei/sigma-vm-images.git
supplychain-fabric-workspace,cox-cei/supplychain-fabric-workspace,Supply chain repo for fabric workspaces,True,Python,2025-02-12T18:32:08Z,2025-06-13T16:58:53Z,2025-06-13T16:58:49Z,660,0,0,0,0,main,False,False,https://github.com/cox-cei/supplychain-fabric-workspace,https://github.com/cox-cei/supplychain-fabric-workspace.git
SupplyChainPOC-TestFramework,cox-cei/SupplyChainPOC-TestFramework,,True,Python,2025-01-29T20:55:39Z,2025-04-14T18:08:57Z,2025-06-03T16:32:28Z,170298,0,0,0,0,main,False,False,https://github.com/cox-cei/SupplyChainPOC-TestFramework,https://github.com/cox-cei/SupplyChainPOC-TestFramework.git
TargetProcess-DataAutomate-Script,cox-cei/TargetProcess-DataAutomate-Script,Create a table using enriched text from TargetProcess,True,Python,2025-07-18T12:30:49Z,2025-07-31T12:59:49Z,2025-07-31T12:59:45Z,100,0,0,0,0,main,False,False,https://github.com/cox-cei/TargetProcess-DataAutomate-Script,https://github.com/cox-cei/TargetProcess-DataAutomate-Script.git
targetprocess-integration-test,cox-cei/targetprocess-integration-test,None,True,,2023-09-21T20:21:30Z,2025-07-22T06:11:39Z,2025-07-22T06:11:37Z,44,0,0,0,3,main,False,False,https://github.com/cox-cei/targetprocess-integration-test,https://github.com/cox-cei/targetprocess-integration-test.git
terraform-aws-lz-core-application,cox-cei/terraform-aws-lz-core-application,None,True,HCL,2024-03-28T22:44:59Z,2024-10-18T20:21:22Z,2024-04-01T14:30:57Z,16,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-lz-core-application,https://github.com/cox-cei/terraform-aws-lz-core-application.git
terraform-aws-lz-core-network,cox-cei/terraform-aws-lz-core-network,None,True,HCL,2024-03-28T22:45:53Z,2024-10-18T23:48:25Z,2024-04-01T14:33:58Z,31,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-lz-core-network,https://github.com/cox-cei/terraform-aws-lz-core-network.git
terraform-aws-lz-core-security,cox-cei/terraform-aws-lz-core-security,None,True,HCL,2024-03-28T22:41:09Z,2024-10-18T20:21:46Z,2024-04-01T14:47:46Z,9,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-lz-core-security,https://github.com/cox-cei/terraform-aws-lz-core-security.git
terraform-aws-mod-cloudwatch,cox-cei/terraform-aws-mod-cloudwatch,None,True,HCL,2024-03-28T22:41:56Z,2024-10-18T20:22:02Z,2024-04-01T11:51:00Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-cloudwatch,https://github.com/cox-cei/terraform-aws-mod-cloudwatch.git
terraform-aws-mod-compute,cox-cei/terraform-aws-mod-compute,None,True,HCL,2024-07-31T16:22:23Z,2024-10-18T23:49:02Z,2024-09-09T19:13:25Z,18,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-compute,https://github.com/cox-cei/terraform-aws-mod-compute.git
terraform-aws-mod-ebs,cox-cei/terraform-aws-mod-ebs,None,True,HCL,2024-03-28T22:45:12Z,2024-10-18T23:49:21Z,2024-03-29T15:04:45Z,13,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-ebs,https://github.com/cox-cei/terraform-aws-mod-ebs.git
terraform-aws-mod-ec2,cox-cei/terraform-aws-mod-ec2,None,True,HCL,2024-03-28T22:42:00Z,2024-10-18T20:22:28Z,2024-07-31T09:57:23Z,16,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-ec2,https://github.com/cox-cei/terraform-aws-mod-ec2.git
terraform-aws-mod-elastic-ip,cox-cei/terraform-aws-mod-elastic-ip,None,True,HCL,2024-03-28T22:45:03Z,2024-10-18T20:22:44Z,2024-04-01T11:56:21Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-elastic-ip,https://github.com/cox-cei/terraform-aws-mod-elastic-ip.git
terraform-aws-mod-elastic-ip-associate,cox-cei/terraform-aws-mod-elastic-ip-associate,None,True,HCL,2024-03-28T22:46:56Z,2024-10-18T23:49:38Z,2024-04-01T11:58:54Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-elastic-ip-associate,https://github.com/cox-cei/terraform-aws-mod-elastic-ip-associate.git
terraform-aws-mod-internet-gateway,cox-cei/terraform-aws-mod-internet-gateway,None,True,HCL,2024-03-28T22:46:39Z,2024-10-18T23:50:15Z,2024-04-01T12:00:54Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-internet-gateway,https://github.com/cox-cei/terraform-aws-mod-internet-gateway.git
terraform-aws-mod-nat-gateway,cox-cei/terraform-aws-mod-nat-gateway,None,True,HCL,2024-03-28T22:46:41Z,2024-10-18T23:50:33Z,2024-04-01T12:02:39Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-nat-gateway,https://github.com/cox-cei/terraform-aws-mod-nat-gateway.git
terraform-aws-mod-route,cox-cei/terraform-aws-mod-route,None,True,HCL,2024-03-28T22:45:59Z,2024-10-18T23:51:11Z,2024-04-01T12:03:53Z,4,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-route,https://github.com/cox-cei/terraform-aws-mod-route.git
terraform-aws-mod-route-table,cox-cei/terraform-aws-mod-route-table,None,True,HCL,2024-03-28T22:41:21Z,2024-10-18T20:23:23Z,2024-04-01T12:05:41Z,4,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-route-table,https://github.com/cox-cei/terraform-aws-mod-route-table.git
terraform-aws-mod-route-table-association,cox-cei/terraform-aws-mod-route-table-association,None,True,HCL,2024-03-28T22:45:49Z,2024-10-18T23:50:57Z,2024-04-01T12:07:49Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-route-table-association,https://github.com/cox-cei/terraform-aws-mod-route-table-association.git
terraform-aws-mod-security-group,cox-cei/terraform-aws-mod-security-group,None,True,HCL,2024-03-28T22:42:13Z,2024-10-18T20:23:26Z,2024-03-29T15:02:36Z,53,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-security-group,https://github.com/cox-cei/terraform-aws-mod-security-group.git
terraform-aws-mod-service-control-policies,cox-cei/terraform-aws-mod-service-control-policies,None,True,HCL,2024-03-28T22:41:17Z,2024-10-18T20:24:07Z,2024-04-01T12:09:28Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-service-control-policies,https://github.com/cox-cei/terraform-aws-mod-service-control-policies.git
terraform-aws-mod-service-control-policies-attachment,cox-cei/terraform-aws-mod-service-control-policies-attachment,None,True,HCL,2024-03-28T22:45:14Z,2024-10-18T23:51:37Z,2024-04-01T12:11:38Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-service-control-policies-attachment,https://github.com/cox-cei/terraform-aws-mod-service-control-policies-attachment.git
terraform-aws-mod-sso-account-assignment,cox-cei/terraform-aws-mod-sso-account-assignment,None,True,HCL,2024-03-28T22:46:45Z,2024-10-18T23:52:01Z,2024-04-01T12:13:50Z,5,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-sso-account-assignment,https://github.com/cox-cei/terraform-aws-mod-sso-account-assignment.git
terraform-aws-mod-sso-permission-set,cox-cei/terraform-aws-mod-sso-permission-set,None,True,HCL,2024-03-28T22:45:01Z,2024-10-18T20:24:16Z,2024-04-01T12:15:29Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-sso-permission-set,https://github.com/cox-cei/terraform-aws-mod-sso-permission-set.git
terraform-aws-mod-storage,cox-cei/terraform-aws-mod-storage,None,True,HCL,2024-08-06T18:22:53Z,2024-10-18T23:52:20Z,2024-09-09T19:49:30Z,9,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-storage,https://github.com/cox-cei/terraform-aws-mod-storage.git
terraform-aws-mod-vpc,cox-cei/terraform-aws-mod-vpc,None,True,HCL,2024-03-28T22:46:05Z,2024-10-18T23:53:53Z,2024-04-01T12:20:26Z,10,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-vpc,https://github.com/cox-cei/terraform-aws-mod-vpc.git
terraform-aws-mod-vpc-dhcp-option,cox-cei/terraform-aws-mod-vpc-dhcp-option,None,True,HCL,2024-03-28T22:46:01Z,2024-10-18T23:52:38Z,2024-04-01T12:22:07Z,4,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-vpc-dhcp-option,https://github.com/cox-cei/terraform-aws-mod-vpc-dhcp-option.git
terraform-aws-mod-vpc-nacl,cox-cei/terraform-aws-mod-vpc-nacl,None,True,HCL,2024-03-28T22:46:54Z,2024-10-18T23:53:09Z,2024-04-01T12:24:39Z,4,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-vpc-nacl,https://github.com/cox-cei/terraform-aws-mod-vpc-nacl.git
terraform-aws-mod-vpc-subnet,cox-cei/terraform-aws-mod-vpc-subnet,None,True,HCL,2024-03-28T22:46:37Z,2024-10-18T23:53:16Z,2024-04-01T12:27:17Z,5,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-mod-vpc-subnet,https://github.com/cox-cei/terraform-aws-mod-vpc-subnet.git
terraform-aws-networking-template,cox-cei/terraform-aws-networking-template,None,True,HCL,2024-03-28T22:41:23Z,2024-10-18T20:24:55Z,2024-04-01T11:32:22Z,10,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-aws-networking-template,https://github.com/cox-cei/terraform-aws-networking-template.git
terraform-azu-lz-core-application,cox-cei/terraform-azu-lz-core-application,None,True,HCL,2024-03-28T22:42:15Z,2024-10-18T20:24:57Z,2024-04-24T16:13:42Z,25,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-lz-core-application,https://github.com/cox-cei/terraform-azu-lz-core-application.git
terraform-azu-lz-core-management,cox-cei/terraform-azu-lz-core-management,None,True,HCL,2024-03-28T22:47:25Z,2024-10-18T23:54:14Z,2024-07-23T13:45:00Z,36,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-lz-core-management,https://github.com/cox-cei/terraform-azu-lz-core-management.git
terraform-azu-lz-core-networking,cox-cei/terraform-azu-lz-core-networking,None,True,HCL,2024-03-28T22:45:05Z,2024-10-18T20:25:36Z,2024-07-05T23:21:15Z,100,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-lz-core-networking,https://github.com/cox-cei/terraform-azu-lz-core-networking.git
terraform-azu-lz-core-security,cox-cei/terraform-azu-lz-core-security,None,True,HCL,2024-03-28T22:42:07Z,2024-10-18T20:25:37Z,2024-07-15T20:45:05Z,35,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-lz-core-security,https://github.com/cox-cei/terraform-azu-lz-core-security.git
terraform-azu-management,cox-cei/terraform-azu-management,None,True,HCL,2024-07-24T16:46:50Z,2025-04-24T14:11:58Z,2025-04-24T14:11:54Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-management,https://github.com/cox-cei/terraform-azu-management.git
terraform-azu-mod-agw,cox-cei/terraform-azu-mod-agw,None,True,HCL,2024-03-28T22:41:11Z,2024-10-18T20:26:10Z,2024-06-28T23:22:20Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-agw,https://github.com/cox-cei/terraform-azu-mod-agw.git
terraform-azu-mod-ai-foundry,cox-cei/terraform-azu-mod-ai-foundry,,True,HCL,2025-03-21T15:25:03Z,2025-04-30T18:59:11Z,2025-05-13T00:14:08Z,10,0,0,0,1,main,False,False,https://github.com/cox-cei/terraform-azu-mod-ai-foundry,https://github.com/cox-cei/terraform-azu-mod-ai-foundry.git
terraform-azu-mod-aiServices,cox-cei/terraform-azu-mod-aiServices,Module for AI services,True,HCL,2025-05-14T14:45:00Z,2025-05-14T20:36:14Z,2025-05-14T20:39:02Z,13,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-aiServices,https://github.com/cox-cei/terraform-azu-mod-aiServices.git
terraform-azu-mod-aml,cox-cei/terraform-azu-mod-aml,None,True,HCL,2024-08-05T19:30:22Z,2025-03-24T21:25:22Z,2025-04-16T21:58:39Z,5,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-aml,https://github.com/cox-cei/terraform-azu-mod-aml.git
Terraform-azu-mod-app-configuration,cox-cei/Terraform-azu-mod-app-configuration,,True,HCL,2025-02-05T14:19:24Z,2025-03-24T22:42:16Z,2025-03-24T22:42:13Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/Terraform-azu-mod-app-configuration,https://github.com/cox-cei/Terraform-azu-mod-app-configuration.git
terraform-azu-mod-app-ins,cox-cei/terraform-azu-mod-app-ins,None,True,HCL,2024-06-24T19:42:16Z,2025-05-01T23:31:17Z,2025-05-01T23:31:14Z,11,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-app-ins,https://github.com/cox-cei/terraform-azu-mod-app-ins.git
terraform-azu-mod-app-service,cox-cei/terraform-azu-mod-app-service,None,True,HCL,2024-03-28T22:41:40Z,2025-05-01T23:28:31Z,2025-05-01T23:28:28Z,16,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-app-service,https://github.com/cox-cei/terraform-azu-mod-app-service.git
terraform-azu-mod-app-service-env,cox-cei/terraform-azu-mod-app-service-env,None,True,HCL,2024-03-28T22:45:08Z,2024-10-18T23:55:24Z,2024-06-28T23:27:03Z,4,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-app-service-env,https://github.com/cox-cei/terraform-azu-mod-app-service-env.git
terraform-azu-mod-app-service-plan,cox-cei/terraform-azu-mod-app-service-plan,None,True,HCL,2024-03-28T22:41:15Z,2025-05-01T23:25:22Z,2025-05-01T23:25:19Z,13,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-app-service-plan,https://github.com/cox-cei/terraform-azu-mod-app-service-plan.git
terraform-azu-mod-cog-search,cox-cei/terraform-azu-mod-cog-search,None,True,HCL,2024-06-24T19:42:08Z,2025-05-01T23:11:45Z,2025-05-01T23:11:42Z,31,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-cog-search,https://github.com/cox-cei/terraform-azu-mod-cog-search.git
terraform-azu-mod-compute,cox-cei/terraform-azu-mod-compute,None,True,HCL,2024-07-24T20:00:13Z,2025-03-03T16:42:52Z,2025-03-03T16:45:34Z,20,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-compute,https://github.com/cox-cei/terraform-azu-mod-compute.git
terraform-azu-mod-con-reg,cox-cei/terraform-azu-mod-con-reg,None,True,HCL,2024-06-24T19:42:06Z,2025-05-01T23:34:22Z,2025-05-01T23:34:19Z,31,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-con-reg,https://github.com/cox-cei/terraform-azu-mod-con-reg.git
terraform-azu-mod-cosmos-db,cox-cei/terraform-azu-mod-cosmos-db,None,True,HCL,2024-06-24T19:42:18Z,2024-12-16T18:05:06Z,2024-12-16T18:19:35Z,23,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-cosmos-db,https://github.com/cox-cei/terraform-azu-mod-cosmos-db.git
terraform-azu-mod-dataFactory,cox-cei/terraform-azu-mod-dataFactory,Terrafrom Azure Data Factory Module,True,HCL,2023-09-15T17:54:24Z,2025-05-08T12:32:53Z,2025-05-08T12:33:02Z,43,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-dataFactory,https://github.com/cox-cei/terraform-azu-mod-dataFactory.git
terraform-azu-mod-egs-top,cox-cei/terraform-azu-mod-egs-top,None,True,,2024-06-24T19:42:22Z,2024-10-18T23:56:53Z,2024-06-24T19:42:22Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-egs-top,https://github.com/cox-cei/terraform-azu-mod-egs-top.git
terraform-azu-mod-fun-app,cox-cei/terraform-azu-mod-fun-app,None,True,HCL,2024-06-24T19:42:12Z,2025-03-24T16:52:36Z,2025-03-24T16:52:32Z,4,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-fun-app,https://github.com/cox-cei/terraform-azu-mod-fun-app.git
terraform-azu-mod-identity,cox-cei/terraform-azu-mod-identity,None,True,HCL,2024-07-08T15:48:46Z,2025-04-24T14:59:48Z,2025-04-24T15:00:19Z,13,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-identity,https://github.com/cox-cei/terraform-azu-mod-identity.git
terraform-azu-mod-keyVault,cox-cei/terraform-azu-mod-keyVault,Terrafrom Azure Key Vault ,True,HCL,2023-09-21T19:23:06Z,2025-04-30T02:33:52Z,2025-04-30T02:34:46Z,32,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-keyVault,https://github.com/cox-cei/terraform-azu-mod-keyVault.git
terraform-azu-mod-kv,cox-cei/terraform-azu-mod-kv,None,True,HCL,2024-03-28T22:41:13Z,2024-10-18T20:27:53Z,2024-06-28T23:29:13Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-kv,https://github.com/cox-cei/terraform-azu-mod-kv.git
terraform-azu-mod-law,cox-cei/terraform-azu-mod-law,None,True,HCL,2024-03-28T22:42:02Z,2024-10-18T20:28:09Z,2024-06-28T23:31:26Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-law,https://github.com/cox-cei/terraform-azu-mod-law.git
terraform-azu-mod-lb,cox-cei/terraform-azu-mod-lb,None,True,HCL,2024-03-28T22:45:51Z,2024-10-18T23:58:03Z,2024-06-28T23:35:16Z,5,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-lb,https://github.com/cox-cei/terraform-azu-mod-lb.git
Terraform-azu-mod-linux-fun-app,cox-cei/Terraform-azu-mod-linux-fun-app,,True,HCL,2025-02-05T14:22:37Z,2025-03-24T23:01:35Z,2025-03-24T23:01:31Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/Terraform-azu-mod-linux-fun-app,https://github.com/cox-cei/Terraform-azu-mod-linux-fun-app.git
Terraform-azu-mod-linux-web-app,cox-cei/Terraform-azu-mod-linux-web-app,,True,,2025-02-05T14:16:03Z,2025-02-05T14:16:05Z,2025-02-05T14:16:04Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/Terraform-azu-mod-linux-web-app,https://github.com/cox-cei/Terraform-azu-mod-linux-web-app.git
terraform-azu-mod-log-app,cox-cei/terraform-azu-mod-log-app,None,True,HCL,2024-06-24T19:42:20Z,2025-05-01T23:35:31Z,2025-05-01T23:35:27Z,9,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-log-app,https://github.com/cox-cei/terraform-azu-mod-log-app.git
terraform-azu-mod-management-lock,cox-cei/terraform-azu-mod-management-lock,None,True,HCL,2024-07-15T20:26:06Z,2024-10-18T23:58:37Z,2024-07-15T20:31:15Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-management-lock,https://github.com/cox-cei/terraform-azu-mod-management-lock.git
terraform-azu-mod-ml-work,cox-cei/terraform-azu-mod-ml-work,None,True,HCL,2024-06-24T19:42:14Z,2024-10-18T23:58:42Z,2024-06-27T04:34:40Z,5,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-ml-work,https://github.com/cox-cei/terraform-azu-mod-ml-work.git
terraform-azu-mod-monitor-diag,cox-cei/terraform-azu-mod-monitor-diag,None,True,HCL,2024-03-28T22:42:11Z,2024-10-18T20:28:32Z,2024-06-28T23:36:30Z,5,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-monitor-diag,https://github.com/cox-cei/terraform-azu-mod-monitor-diag.git
terraform-azu-mod-mssql,cox-cei/terraform-azu-mod-mssql,None,True,HCL,2024-03-28T22:46:47Z,2024-10-18T23:59:15Z,2024-06-28T23:37:32Z,13,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-mssql,https://github.com/cox-cei/terraform-azu-mod-mssql.git
Terraform-azu-mod-mssql-database,cox-cei/Terraform-azu-mod-mssql-database,,True,HCL,2025-02-05T14:13:29Z,2025-03-24T22:52:38Z,2025-03-24T22:52:34Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/Terraform-azu-mod-mssql-database,https://github.com/cox-cei/Terraform-azu-mod-mssql-database.git
Terraform-azu-mod-mssql-server,cox-cei/Terraform-azu-mod-mssql-server,,True,HCL,2025-02-05T14:11:03Z,2025-03-24T22:49:12Z,2025-03-24T22:49:08Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/Terraform-azu-mod-mssql-server,https://github.com/cox-cei/Terraform-azu-mod-mssql-server.git
terraform-azu-mod-mssqlDataBase,cox-cei/terraform-azu-mod-mssqlDataBase,Terrafrom Azure SQL Database Module,True,HCL,2023-09-15T17:54:22Z,2024-09-20T19:21:57Z,2023-10-06T18:41:29Z,27,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-mssqlDataBase,https://github.com/cox-cei/terraform-azu-mod-mssqlDataBase.git
terraform-azu-mod-natgw,cox-cei/terraform-azu-mod-natgw,None,True,HCL,2024-03-28T22:47:27Z,2024-10-18T23:59:22Z,2024-06-28T23:38:48Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-natgw,https://github.com/cox-cei/terraform-azu-mod-natgw.git
terraform-azu-mod-nsg,cox-cei/terraform-azu-mod-nsg,None,True,HCL,2024-03-28T22:46:43Z,2024-10-18T23:59:50Z,2024-06-28T23:39:59Z,13,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-nsg,https://github.com/cox-cei/terraform-azu-mod-nsg.git
terraform-azu-mod-policy-assignment,cox-cei/terraform-azu-mod-policy-assignment,None,True,HCL,2024-03-28T22:45:55Z,2024-10-18T23:59:56Z,2024-06-28T23:40:57Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-policy-assignment,https://github.com/cox-cei/terraform-azu-mod-policy-assignment.git
terraform-azu-mod-policy-definition,cox-cei/terraform-azu-mod-policy-definition,None,True,HCL,2024-03-28T22:45:07Z,2024-10-19T00:00:34Z,2024-06-28T23:42:04Z,9,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-policy-definition,https://github.com/cox-cei/terraform-azu-mod-policy-definition.git
terraform-azu-mod-policy-initiative-assignment,cox-cei/terraform-azu-mod-policy-initiative-assignment,None,True,HCL,2024-03-28T22:45:10Z,2024-10-19T00:00:41Z,2024-06-28T23:43:45Z,4,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-policy-initiative-assignment,https://github.com/cox-cei/terraform-azu-mod-policy-initiative-assignment.git
terraform-azu-mod-policy-initiative-definition,cox-cei/terraform-azu-mod-policy-initiative-definition,None,True,HCL,2024-03-28T22:41:25Z,2024-10-18T20:29:08Z,2024-06-28T23:44:51Z,4,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-policy-initiative-definition,https://github.com/cox-cei/terraform-azu-mod-policy-initiative-definition.git
terraform-azu-mod-privateEndPoint,cox-cei/terraform-azu-mod-privateEndPoint,None,True,HCL,2024-03-28T22:45:47Z,2025-04-30T03:01:07Z,2025-04-30T03:01:46Z,17,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-privateEndPoint,https://github.com/cox-cei/terraform-azu-mod-privateEndPoint.git
terraform-azu-mod-public-ip,cox-cei/terraform-azu-mod-public-ip,None,True,HCL,2024-03-28T22:45:16Z,2024-10-19T00:01:23Z,2024-06-28T23:46:47Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-public-ip,https://github.com/cox-cei/terraform-azu-mod-public-ip.git
Terraform-azu-mod-redis-cache,cox-cei/Terraform-azu-mod-redis-cache,,True,HCL,2025-02-05T14:08:27Z,2025-03-24T22:46:41Z,2025-03-24T22:46:37Z,4,0,0,0,0,main,False,False,https://github.com/cox-cei/Terraform-azu-mod-redis-cache,https://github.com/cox-cei/Terraform-azu-mod-redis-cache.git
Terraform-azu-mod-resource-group,cox-cei/Terraform-azu-mod-resource-group,,True,HCL,2025-02-05T13:52:37Z,2025-03-24T23:04:52Z,2025-03-24T23:04:49Z,1,0,0,0,0,main,False,False,https://github.com/cox-cei/Terraform-azu-mod-resource-group,https://github.com/cox-cei/Terraform-azu-mod-resource-group.git
terraform-azu-mod-role-assignment,cox-cei/terraform-azu-mod-role-assignment,None,True,HCL,2024-03-28T22:42:09Z,2024-10-18T20:29:32Z,2024-06-28T23:47:45Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-role-assignment,https://github.com/cox-cei/terraform-azu-mod-role-assignment.git
terraform-azu-mod-rt,cox-cei/terraform-azu-mod-rt,None,True,HCL,2024-03-28T22:46:49Z,2024-10-19T00:02:05Z,2024-06-28T23:48:39Z,4,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-rt,https://github.com/cox-cei/terraform-azu-mod-rt.git
terraform-azu-mod-sa,cox-cei/terraform-azu-mod-sa,None,True,HCL,2024-03-28T22:45:57Z,2025-05-07T12:32:30Z,2025-05-07T13:00:58Z,53,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-sa,https://github.com/cox-cei/terraform-azu-mod-sa.git
terraform-azu-mod-search-serv,cox-cei/terraform-azu-mod-search-serv,None,True,HCL,2024-06-24T19:42:10Z,2025-05-01T23:06:47Z,2025-05-01T23:06:43Z,29,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-search-serv,https://github.com/cox-cei/terraform-azu-mod-search-serv.git
terraform-azu-mod-sqlDataBase,cox-cei/terraform-azu-mod-sqlDataBase,None,True,HCL,2024-09-23T17:07:43Z,2025-05-08T12:28:02Z,2025-05-08T12:38:15Z,26,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-sqlDataBase,https://github.com/cox-cei/terraform-azu-mod-sqlDataBase.git
terraform-azu-mod-storage,cox-cei/terraform-azu-mod-storage,None,True,HCL,2024-07-30T14:09:03Z,2025-03-04T12:29:27Z,2025-03-04T12:32:46Z,8,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-storage,https://github.com/cox-cei/terraform-azu-mod-storage.git
terraform-azu-mod-subnet,cox-cei/terraform-azu-mod-subnet,None,True,HCL,2024-03-28T22:41:19Z,2024-10-18T20:29:54Z,2024-07-01T14:08:34Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-subnet,https://github.com/cox-cei/terraform-azu-mod-subnet.git
terraform-azu-mod-vm,cox-cei/terraform-azu-mod-vm,None,True,HCL,2024-03-28T22:46:51Z,2024-10-19T00:03:39Z,2024-06-28T23:51:18Z,11,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-vm,https://github.com/cox-cei/terraform-azu-mod-vm.git
terraform-azu-mod-vnet,cox-cei/terraform-azu-mod-vnet,None,True,HCL,2024-03-28T22:46:03Z,2024-10-19T00:03:44Z,2024-06-28T23:52:15Z,4,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-vnet,https://github.com/cox-cei/terraform-azu-mod-vnet.git
terraform-azu-mod-vnet-peering,cox-cei/terraform-azu-mod-vnet-peering,None,True,HCL,2024-03-28T22:42:05Z,2024-10-18T20:30:19Z,2024-06-28T23:53:16Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-mod-vnet-peering,https://github.com/cox-cei/terraform-azu-mod-vnet-peering.git
Terraform-azu-mssql-managed-instance,cox-cei/Terraform-azu-mssql-managed-instance,,True,,2025-03-24T21:37:34Z,2025-03-24T21:37:36Z,2025-03-24T21:37:35Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/Terraform-azu-mssql-managed-instance,https://github.com/cox-cei/Terraform-azu-mssql-managed-instance.git
terraform-azu-network,cox-cei/terraform-azu-network,,True,,2025-07-15T18:29:35Z,2025-07-15T18:29:37Z,2025-07-15T18:29:35Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-network,https://github.com/cox-cei/terraform-azu-network.git
terraform-azu-networks,cox-cei/terraform-azu-networks,,True,HCL,2025-07-15T18:48:06Z,2025-07-16T11:36:00Z,2025-07-16T11:35:56Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azu-networks,https://github.com/cox-cei/terraform-azu-networks.git
terraform-azure-aifactory,cox-cei/terraform-azure-aifactory,None,True,HCL,2024-05-17T16:45:50Z,2025-05-15T19:17:20Z,2025-05-15T19:17:17Z,94,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azure-aifactory,https://github.com/cox-cei/terraform-azure-aifactory.git
terraform-azure-storageaccount,cox-cei/terraform-azure-storageaccount,Azure Terraform Module for Storage Accounts,True,HCL,2023-09-05T19:38:44Z,2024-12-16T18:24:36Z,2024-12-16T18:33:23Z,37,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-azure-storageaccount,https://github.com/cox-cei/terraform-azure-storageaccount.git
terraform-deployment-template,cox-cei/terraform-deployment-template,,True,HCL,2023-09-20T15:49:54Z,2023-09-20T20:20:27Z,2023-09-21T13:45:16Z,8,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-deployment-template,https://github.com/cox-cei/terraform-deployment-template.git
terraform-gcp-management,cox-cei/terraform-gcp-management,None,True,HCL,2024-09-06T14:23:57Z,2024-11-04T20:22:45Z,2024-11-04T20:22:47Z,22,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-gcp-management,https://github.com/cox-cei/terraform-gcp-management.git
terraform-gcp-mod-apigee,cox-cei/terraform-gcp-mod-apigee,GCP ApigeeX Full Environment,True,HCL,2024-11-26T21:06:55Z,2025-05-02T18:20:20Z,2025-05-02T18:20:19Z,106,1,1,0,3,main,False,False,https://github.com/cox-cei/terraform-gcp-mod-apigee,https://github.com/cox-cei/terraform-gcp-mod-apigee.git
terraform-gcp-mod-compute,cox-cei/terraform-gcp-mod-compute,None,True,HCL,2024-10-08T12:42:50Z,2025-04-02T16:54:53Z,2025-04-02T16:54:50Z,68,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-gcp-mod-compute,https://github.com/cox-cei/terraform-gcp-mod-compute.git
terraform-gcp-mod-identity,cox-cei/terraform-gcp-mod-identity,None,True,HCL,2024-09-10T19:04:53Z,2025-01-13T15:08:05Z,2025-01-13T15:08:04Z,13,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-gcp-mod-identity,https://github.com/cox-cei/terraform-gcp-mod-identity.git
terraform-gcp-network,cox-cei/terraform-gcp-network,GCP Core Networking Components,True,HCL,2024-11-14T14:35:47Z,2025-01-23T20:09:47Z,2025-06-04T18:14:34Z,37720,1,1,0,1,main,False,False,https://github.com/cox-cei/terraform-gcp-network,https://github.com/cox-cei/terraform-gcp-network.git
terraform-mod-cei,cox-cei/terraform-mod-cei,None,True,HCL,2024-08-10T17:13:51Z,2025-08-01T18:37:37Z,2025-08-01T18:37:34Z,198,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-mod-cei,https://github.com/cox-cei/terraform-mod-cei.git
terraform-mod-compute,cox-cei/terraform-mod-compute,None,True,HCL,2024-07-24T16:46:48Z,2025-08-01T18:37:14Z,2025-08-01T18:37:13Z,102,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-mod-compute,https://github.com/cox-cei/terraform-mod-compute.git
terraform-mod-onefuse,cox-cei/terraform-mod-onefuse,None,True,HCL,2024-04-23T14:30:39Z,2024-10-19T00:05:05Z,2024-09-16T15:31:50Z,30,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-mod-onefuse,https://github.com/cox-cei/terraform-mod-onefuse.git
terraform-mod-servicenow,cox-cei/terraform-mod-servicenow,repo for SNOW tf configuration,True,HCL,2024-04-23T14:58:51Z,2025-07-11T14:36:04Z,2025-07-11T14:36:01Z,11,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-mod-servicenow,https://github.com/cox-cei/terraform-mod-servicenow.git
terraform-mod-terraform,cox-cei/terraform-mod-terraform,None,True,HCL,2024-10-15T19:06:02Z,2025-07-03T16:06:36Z,2025-07-03T16:09:01Z,128,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-mod-terraform,https://github.com/cox-cei/terraform-mod-terraform.git
terraform-mod-thomasTest,cox-cei/terraform-mod-thomasTest,None,True,HCL,2024-09-19T14:57:36Z,2024-10-19T00:44:04Z,2024-09-20T15:18:27Z,1,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-mod-thomasTest,https://github.com/cox-cei/terraform-mod-thomasTest.git
terraform-mod-thomasTest1,cox-cei/terraform-mod-thomasTest1,None,True,HCL,2024-09-20T13:47:10Z,2024-10-19T00:44:08Z,2024-09-20T14:45:42Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-mod-thomasTest1,https://github.com/cox-cei/terraform-mod-thomasTest1.git
terraform-mod-thomasTest2,cox-cei/terraform-mod-thomasTest2,None,True,HCL,2024-09-20T13:47:16Z,2024-10-19T00:44:42Z,2024-09-20T14:45:54Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-mod-thomasTest2,https://github.com/cox-cei/terraform-mod-thomasTest2.git
terraform-mod-thomasTest3,cox-cei/terraform-mod-thomasTest3,None,True,HCL,2024-09-20T13:47:08Z,2024-10-19T00:44:48Z,2024-09-20T14:46:06Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-mod-thomasTest3,https://github.com/cox-cei/terraform-mod-thomasTest3.git
terraform-mod-thomasTest4,cox-cei/terraform-mod-thomasTest4,None,True,HCL,2024-09-20T13:47:14Z,2024-10-19T00:45:22Z,2024-09-20T14:46:17Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-mod-thomasTest4,https://github.com/cox-cei/terraform-mod-thomasTest4.git
terraform-mod-thomasTest5,cox-cei/terraform-mod-thomasTest5,None,True,HCL,2024-09-20T13:47:12Z,2024-10-19T00:45:29Z,2024-09-20T14:46:27Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-mod-thomasTest5,https://github.com/cox-cei/terraform-mod-thomasTest5.git
terraform-module-template,cox-cei/terraform-module-template,,True,HCL,2023-09-05T19:38:49Z,2023-09-15T15:55:34Z,2023-09-21T13:38:20Z,8,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-module-template,https://github.com/cox-cei/terraform-module-template.git
terraform-oci-core-applications,cox-cei/terraform-oci-core-applications,None,True,HCL,2024-02-09T17:54:22Z,2024-10-18T20:30:59Z,2024-04-12T15:42:02Z,293,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-core-applications,https://github.com/cox-cei/terraform-oci-core-applications.git
terraform-oci-core-management,cox-cei/terraform-oci-core-management,None,True,HCL,2024-02-09T17:53:27Z,2024-10-18T20:31:03Z,2024-06-28T18:55:23Z,63,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-core-management,https://github.com/cox-cei/terraform-oci-core-management.git
terraform-oci-core-security,cox-cei/terraform-oci-core-security,None,True,HCL,2024-02-09T17:54:05Z,2024-10-18T20:31:36Z,2024-06-18T13:02:14Z,8,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-core-security,https://github.com/cox-cei/terraform-oci-core-security.git
terraform-oci-management,cox-cei/terraform-oci-management,None,True,HCL,2024-04-26T19:00:40Z,2025-07-10T12:31:39Z,2025-07-10T12:38:36Z,7,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-management,https://github.com/cox-cei/terraform-oci-management.git
terraform-oci-mod-compute,cox-cei/terraform-oci-mod-compute,None,True,HCL,2024-02-09T17:54:07Z,2025-07-10T12:31:51Z,2025-07-10T12:54:34Z,98,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-mod-compute,https://github.com/cox-cei/terraform-oci-mod-compute.git
terraform-oci-mod-database,cox-cei/terraform-oci-mod-database,None,True,HCL,2024-02-09T17:53:32Z,2025-07-28T19:22:41Z,2025-07-28T19:23:04Z,120,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-mod-database,https://github.com/cox-cei/terraform-oci-mod-database.git
terraform-oci-mod-governance,cox-cei/terraform-oci-mod-governance,None,True,HCL,2024-02-09T17:53:38Z,2024-10-18T20:32:24Z,2024-02-12T16:20:08Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-mod-governance,https://github.com/cox-cei/terraform-oci-mod-governance.git
terraform-oci-mod-identity,cox-cei/terraform-oci-mod-identity,None,True,HCL,2024-02-09T17:53:34Z,2025-07-10T12:32:11Z,2025-07-10T12:45:15Z,14,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-mod-identity,https://github.com/cox-cei/terraform-oci-mod-identity.git
terraform-oci-mod-ip,cox-cei/terraform-oci-mod-ip,None,True,HCL,2024-02-09T17:53:54Z,2024-10-18T20:33:10Z,2024-02-12T16:57:59Z,2,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-mod-ip,https://github.com/cox-cei/terraform-oci-mod-ip.git
terraform-oci-mod-loadbalancer,cox-cei/terraform-oci-mod-loadbalancer,None,True,HCL,2024-02-09T17:53:29Z,2024-10-18T20:33:23Z,2024-02-12T17:02:14Z,9,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-mod-loadbalancer,https://github.com/cox-cei/terraform-oci-mod-loadbalancer.git
terraform-oci-mod-managementservices,cox-cei/terraform-oci-mod-managementservices,None,True,HCL,2024-02-09T17:53:36Z,2025-07-10T12:32:21Z,2025-07-10T12:50:52Z,6,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-mod-managementservices,https://github.com/cox-cei/terraform-oci-mod-managementservices.git
terraform-oci-mod-network,cox-cei/terraform-oci-mod-network,None,True,HCL,2024-04-26T23:52:29Z,2025-07-10T12:33:33Z,2025-07-10T12:50:17Z,28,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-mod-network,https://github.com/cox-cei/terraform-oci-mod-network.git
terraform-oci-mod-storage,cox-cei/terraform-oci-mod-storage,None,True,HCL,2024-02-09T17:53:25Z,2025-07-10T12:33:04Z,2025-07-10T12:47:25Z,58,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-mod-storage,https://github.com/cox-cei/terraform-oci-mod-storage.git
terraform-oci-network,cox-cei/terraform-oci-network,None,True,HCL,2024-04-26T15:21:01Z,2024-10-19T00:06:36Z,2024-09-25T18:01:03Z,997,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-network,https://github.com/cox-cei/terraform-oci-network.git
terraform-oci-oracleEBSBI,cox-cei/terraform-oci-oracleEBSBI,None,True,HCL,2024-03-18T21:25:46Z,2024-10-18T20:34:18Z,2024-08-14T20:51:45Z,44,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-oracleEBSBI,https://github.com/cox-cei/terraform-oci-oracleEBSBI.git
terraform-oci-oracleEBSFinancials,cox-cei/terraform-oci-oracleEBSFinancials,None,True,HCL,2024-03-13T20:47:25Z,2024-12-13T15:50:57Z,2024-12-13T15:50:52Z,52,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-oracleEBSFinancials,https://github.com/cox-cei/terraform-oci-oracleEBSFinancials.git
terraform-oci-oracleOICAgent,cox-cei/terraform-oci-oracleOICAgent,None,True,HCL,2024-04-11T18:32:59Z,2024-10-19T00:06:40Z,2024-08-14T20:52:24Z,35,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-oracleOICAgent,https://github.com/cox-cei/terraform-oci-oracleOICAgent.git
terraform-oci-oracleVertex,cox-cei/terraform-oci-oracleVertex,None,True,HCL,2024-04-11T18:32:57Z,2024-10-19T00:07:21Z,2024-08-14T20:53:03Z,41,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-oracleVertex,https://github.com/cox-cei/terraform-oci-oracleVertex.git
terraform-oci-oracleWebcenter,cox-cei/terraform-oci-oracleWebcenter,None,True,HCL,2024-04-11T15:28:36Z,2024-12-13T15:51:52Z,2024-12-13T15:51:48Z,44,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-oci-oracleWebcenter,https://github.com/cox-cei/terraform-oci-oracleWebcenter.git
terraform-provider-sectigo,cox-cei/terraform-provider-sectigo,Provider content for Sectico to import into terraform cloud private registry,True,HCL,2025-01-23T20:22:49Z,2025-01-25T19:31:15Z,2025-01-26T03:28:15Z,58018,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-provider-sectigo,https://github.com/cox-cei/terraform-provider-sectigo.git
terraform-sentinel,cox-cei/terraform-sentinel,None,True,HCL,2024-08-16T17:51:11Z,2024-10-19T00:45:59Z,2025-05-22T12:51:28Z,32,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-sentinel,https://github.com/cox-cei/terraform-sentinel.git
terraform-sentinelCloud,cox-cei/terraform-sentinelCloud,None,True,HCL,2024-01-26T19:20:41Z,2024-10-18T20:34:55Z,2024-01-26T19:50:01Z,116,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-sentinelCloud,https://github.com/cox-cei/terraform-sentinelCloud.git
terraform-terraformCloud,cox-cei/terraform-terraformCloud,None,True,HCL,2024-01-25T19:44:19Z,2024-10-18T20:35:19Z,2024-05-29T16:44:39Z,14339,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-terraformCloud,https://github.com/cox-cei/terraform-terraformCloud.git
terraform-vma-mod-compute,cox-cei/terraform-vma-mod-compute,None,True,HCL,2024-08-29T13:47:49Z,2025-08-01T15:11:15Z,2025-08-01T15:11:13Z,18,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-vma-mod-compute,https://github.com/cox-cei/terraform-vma-mod-compute.git
terraform-vraGovernance,cox-cei/terraform-vraGovernance,None,True,HCL,2024-02-21T16:33:02Z,2025-04-24T15:56:56Z,2025-04-24T15:56:52Z,49,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-vraGovernance,https://github.com/cox-cei/terraform-vraGovernance.git
terraform-zeroTrust,cox-cei/terraform-zeroTrust,None,True,HCL,2023-11-29T19:50:49Z,2024-10-18T20:36:17Z,2025-03-02T21:59:58Z,44,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-zeroTrust,https://github.com/cox-cei/terraform-zeroTrust.git
terraform-zeroTrust-Governance,cox-cei/terraform-zeroTrust-Governance,None,True,HCL,2024-01-24T19:34:46Z,2024-10-18T20:36:09Z,2024-01-24T21:45:24Z,7235,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform-zeroTrust-Governance,https://github.com/cox-cei/terraform-zeroTrust-Governance.git
terraform_aws_cloudwatchMod,cox-cei/terraform_aws_cloudwatchMod,None,True,HCL,2023-07-07T15:08:08Z,2024-10-18T20:36:54Z,2023-07-13T18:37:36Z,4,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_cloudwatchMod,https://github.com/cox-cei/terraform_aws_cloudwatchMod.git
terraform_aws_ebsMod,cox-cei/terraform_aws_ebsMod,None,True,HCL,2023-07-07T15:08:09Z,2024-10-18T20:36:59Z,2023-07-13T18:38:34Z,16,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_ebsMod,https://github.com/cox-cei/terraform_aws_ebsMod.git
terraform_aws_ec2Mod,cox-cei/terraform_aws_ec2Mod,None,True,HCL,2023-07-07T15:08:10Z,2024-10-18T20:37:26Z,2023-07-13T18:57:23Z,13,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_ec2Mod,https://github.com/cox-cei/terraform_aws_ec2Mod.git
terraform_aws_elasticIpAssociateMod,cox-cei/terraform_aws_elasticIpAssociateMod,None,True,HCL,2023-07-07T15:08:11Z,2024-10-18T20:37:38Z,2023-07-13T19:00:40Z,4,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_elasticIpAssociateMod,https://github.com/cox-cei/terraform_aws_elasticIpAssociateMod.git
terraform_aws_ElasticIpMod,cox-cei/terraform_aws_ElasticIpMod,None,True,HCL,2023-07-07T15:08:13Z,2024-10-18T20:37:56Z,2023-07-13T19:01:21Z,4,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_ElasticIpMod,https://github.com/cox-cei/terraform_aws_ElasticIpMod.git
terraform_aws_internetGatewayMod,cox-cei/terraform_aws_internetGatewayMod,None,True,HCL,2023-07-07T15:08:14Z,2024-10-18T20:38:13Z,2023-07-13T19:02:22Z,4,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_internetGatewayMod,https://github.com/cox-cei/terraform_aws_internetGatewayMod.git
terraform_aws_lzApplications,cox-cei/terraform_aws_lzApplications,None,True,HCL,2023-07-07T15:08:16Z,2024-10-18T20:38:36Z,2023-12-13T17:31:46Z,21,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_lzApplications,https://github.com/cox-cei/terraform_aws_lzApplications.git
terraform_aws_lzCoreNetwork,cox-cei/terraform_aws_lzCoreNetwork,None,True,HCL,2023-07-07T15:08:17Z,2024-10-18T20:38:46Z,2024-01-23T20:00:43Z,59,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_lzCoreNetwork,https://github.com/cox-cei/terraform_aws_lzCoreNetwork.git
terraform_aws_lzCoreSecurity,cox-cei/terraform_aws_lzCoreSecurity,None,True,HCL,2023-07-07T15:08:19Z,2024-10-18T20:39:10Z,2023-12-18T15:57:25Z,30,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_lzCoreSecurity,https://github.com/cox-cei/terraform_aws_lzCoreSecurity.git
terraform_aws_natGatewayMod,cox-cei/terraform_aws_natGatewayMod,None,True,HCL,2023-07-07T15:08:20Z,2024-10-18T20:39:19Z,2023-07-13T19:05:15Z,3,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_natGatewayMod,https://github.com/cox-cei/terraform_aws_natGatewayMod.git
terraform_aws_networkingTemplate,cox-cei/terraform_aws_networkingTemplate,None,True,HCL,2023-07-07T15:08:22Z,2024-10-18T20:39:43Z,2024-01-23T16:02:19Z,19,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_networkingTemplate,https://github.com/cox-cei/terraform_aws_networkingTemplate.git
terraform_aws_routeMod,cox-cei/terraform_aws_routeMod,None,True,HCL,2023-07-07T15:08:23Z,2024-10-18T20:40:00Z,2023-07-13T19:06:15Z,5,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_routeMod,https://github.com/cox-cei/terraform_aws_routeMod.git
terraform_aws_routeTableAssociateMod,cox-cei/terraform_aws_routeTableAssociateMod,None,True,HCL,2023-07-07T15:08:24Z,2024-10-18T20:40:22Z,2023-07-13T19:06:43Z,3,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_routeTableAssociateMod,https://github.com/cox-cei/terraform_aws_routeTableAssociateMod.git
terraform_aws_routeTableMod,cox-cei/terraform_aws_routeTableMod,None,True,HCL,2023-07-07T15:08:25Z,2024-10-18T20:40:39Z,2023-07-13T19:07:55Z,5,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_routeTableMod,https://github.com/cox-cei/terraform_aws_routeTableMod.git
terraform_aws_securityGroupMod,cox-cei/terraform_aws_securityGroupMod,None,True,HCL,2023-07-07T15:58:54Z,2024-10-18T20:40:59Z,2023-07-13T19:08:36Z,54,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_securityGroupMod,https://github.com/cox-cei/terraform_aws_securityGroupMod.git
terraform_aws_serviceControlPoliciesAttachmentMod,cox-cei/terraform_aws_serviceControlPoliciesAttachmentMod,None,True,HCL,2023-07-07T15:58:37Z,2024-10-18T20:41:15Z,2023-07-13T19:09:51Z,3,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_serviceControlPoliciesAttachmentMod,https://github.com/cox-cei/terraform_aws_serviceControlPoliciesAttachmentMod.git
terraform_aws_serviceControlPoliciesMod,cox-cei/terraform_aws_serviceControlPoliciesMod,None,True,HCL,2023-07-07T15:58:17Z,2024-10-18T20:41:38Z,2023-07-13T19:10:23Z,4,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_serviceControlPoliciesMod,https://github.com/cox-cei/terraform_aws_serviceControlPoliciesMod.git
terraform_aws_ssoAccountAssignmentMod,cox-cei/terraform_aws_ssoAccountAssignmentMod,None,True,HCL,2023-07-07T15:57:55Z,2024-10-18T20:41:56Z,2023-07-13T19:11:06Z,6,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_ssoAccountAssignmentMod,https://github.com/cox-cei/terraform_aws_ssoAccountAssignmentMod.git
terraform_aws_ssoPermissionSetMod,cox-cei/terraform_aws_ssoPermissionSetMod,None,True,HCL,2023-07-07T15:08:29Z,2024-10-18T20:42:22Z,2023-07-13T19:11:35Z,7,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_ssoPermissionSetMod,https://github.com/cox-cei/terraform_aws_ssoPermissionSetMod.git
terraform_aws_vpcDhcpOptionMod,cox-cei/terraform_aws_vpcDhcpOptionMod,None,True,HCL,2023-07-07T15:08:30Z,2024-10-18T20:42:40Z,2023-07-13T19:12:16Z,5,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_vpcDhcpOptionMod,https://github.com/cox-cei/terraform_aws_vpcDhcpOptionMod.git
terraform_aws_vpcMod,cox-cei/terraform_aws_vpcMod,None,True,HCL,2023-07-07T15:08:31Z,2024-10-18T20:42:57Z,2023-07-13T19:12:46Z,11,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_vpcMod,https://github.com/cox-cei/terraform_aws_vpcMod.git
terraform_aws_vpcNaclMod,cox-cei/terraform_aws_vpcNaclMod,None,True,HCL,2023-07-07T15:08:33Z,2024-10-18T20:43:19Z,2023-07-13T19:13:08Z,5,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_vpcNaclMod,https://github.com/cox-cei/terraform_aws_vpcNaclMod.git
terraform_aws_vpcSubnetMod,cox-cei/terraform_aws_vpcSubnetMod,None,True,HCL,2023-07-07T15:08:34Z,2024-10-18T20:43:33Z,2023-07-13T19:13:36Z,6,0,0,0,0,master,False,False,https://github.com/cox-cei/terraform_aws_vpcSubnetMod,https://github.com/cox-cei/terraform_aws_vpcSubnetMod.git
terraform_azure_testTerraformPipeline,cox-cei/terraform_azure_testTerraformPipeline,None,True,,2023-07-18T19:59:42Z,2024-10-18T20:43:58Z,2023-07-20T15:09:57Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/terraform_azure_testTerraformPipeline,https://github.com/cox-cei/terraform_azure_testTerraformPipeline.git
test-demo-repo,cox-cei/test-demo-repo,,True,,2025-07-16T14:09:12Z,2025-07-16T14:09:14Z,2025-07-16T14:09:13Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/test-demo-repo,https://github.com/cox-cei/test-demo-repo.git
Test-OCI-CMG,cox-cei/Test-OCI-CMG,None,True,,2024-04-08T17:32:00Z,2024-10-19T00:07:59Z,2024-05-17T07:14:08Z,4,0,0,0,0,develop,False,False,https://github.com/cox-cei/Test-OCI-CMG,https://github.com/cox-cei/Test-OCI-CMG.git
test-repository-deletion,cox-cei/test-repository-deletion,None,True,,2024-07-26T19:22:02Z,2024-10-19T00:08:08Z,2024-07-26T19:22:02Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/test-repository-deletion,https://github.com/cox-cei/test-repository-deletion.git
Test-TFCloud-Governance,cox-cei/Test-TFCloud-Governance,None,True,HCL,2024-08-28T15:35:57Z,2024-10-19T00:46:38Z,2024-08-28T15:55:11Z,585,0,0,0,0,main,False,False,https://github.com/cox-cei/Test-TFCloud-Governance,https://github.com/cox-cei/Test-TFCloud-Governance.git
Test-TFCloud-Import,cox-cei/Test-TFCloud-Import,None,True,,2024-08-28T15:36:38Z,2024-10-19T00:46:40Z,2024-08-28T15:36:38Z,1,0,0,0,0,main,False,False,https://github.com/cox-cei/Test-TFCloud-Import,https://github.com/cox-cei/Test-TFCloud-Import.git
testname,cox-cei/testname,None,True,JavaScript,2023-07-28T15:45:44Z,2024-10-18T20:44:23Z,2023-10-05T18:21:41Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/testname,https://github.com/cox-cei/testname.git
tf-az-private-endpoint,cox-cei/tf-az-private-endpoint,Terrafrom Azure Private Endpoint Module ,True,HCL,2023-09-15T17:54:26Z,2024-07-21T02:12:39Z,2024-09-19T16:39:19Z,32,0,0,0,1,main,False,False,https://github.com/cox-cei/tf-az-private-endpoint,https://github.com/cox-cei/tf-az-private-endpoint.git
tf-az-service-principle,cox-cei/tf-az-service-principle,Terrafrom Azure Service Principle Module,True,HCL,2023-09-15T17:54:18Z,2024-07-21T02:28:59Z,2023-09-22T19:42:57Z,4,0,0,0,0,main,False,False,https://github.com/cox-cei/tf-az-service-principle,https://github.com/cox-cei/tf-az-service-principle.git
tf-az-sql-vm,cox-cei/tf-az-sql-vm,Terrafrom Azure SQL VM ,True,HCL,2023-09-20T13:13:08Z,2024-07-21T03:08:14Z,2023-09-21T17:18:33Z,5,0,0,0,0,main,False,False,https://github.com/cox-cei/tf-az-sql-vm,https://github.com/cox-cei/tf-az-sql-vm.git
tf-az-storage-account,cox-cei/tf-az-storage-account,Terrafrom Azure Storage Account Module,True,HCL,2023-09-15T17:54:20Z,2024-07-21T03:26:31Z,2023-10-11T14:23:19Z,40,0,0,0,0,main,False,False,https://github.com/cox-cei/tf-az-storage-account,https://github.com/cox-cei/tf-az-storage-account.git
tf-az-vnet,cox-cei/tf-az-vnet,Terrafrom Azure vnet ,True,HCL,2023-09-19T19:04:17Z,2024-07-21T03:17:27Z,2023-09-19T19:04:19Z,3,0,0,0,0,main,False,False,https://github.com/cox-cei/tf-az-vnet,https://github.com/cox-cei/tf-az-vnet.git
theGrove-site,cox-cei/theGrove-site,None,True,,2024-10-02T19:15:34Z,2024-10-19T00:47:20Z,2024-10-02T19:15:34Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/theGrove-site,https://github.com/cox-cei/theGrove-site.git
tme-fabric-dc-workspace,cox-cei/tme-fabric-dc-workspace,TME DC Workspace GitHub Repository,True,Python,2025-06-25T16:09:57Z,2025-07-25T12:45:05Z,2025-07-25T12:45:02Z,210,0,0,0,0,main,False,False,https://github.com/cox-cei/tme-fabric-dc-workspace,https://github.com/cox-cei/tme-fabric-dc-workspace.git
tosca-cbss,cox-cei/tosca-cbss,None,True,,2023-12-01T15:03:47Z,2024-10-18T20:46:15Z,2023-12-01T15:03:48Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/tosca-cbss,https://github.com/cox-cei/tosca-cbss.git
tosca-COX_Automation,cox-cei/tosca-COX_Automation,None,True,,2023-12-12T19:17:30Z,2024-10-18T20:46:36Z,2023-12-12T19:17:30Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/tosca-COX_Automation,https://github.com/cox-cei/tosca-COX_Automation.git
Tosca-ExecutionClient-Script,cox-cei/Tosca-ExecutionClient-Script,None,True,Shell,2024-03-21T20:40:49Z,2025-07-23T15:57:49Z,2025-07-23T15:57:45Z,442,1,1,0,0,main,False,False,https://github.com/cox-cei/Tosca-ExecutionClient-Script,https://github.com/cox-cei/Tosca-ExecutionClient-Script.git
tosca-OracleEBS,cox-cei/tosca-OracleEBS,None,True,,2023-12-01T15:03:49Z,2024-10-18T20:47:24Z,2024-02-12T21:55:05Z,1,0,0,0,0,folders-patch-new,False,False,https://github.com/cox-cei/tosca-OracleEBS,https://github.com/cox-cei/tosca-OracleEBS.git
tosca-ServiceNow,cox-cei/tosca-ServiceNow,None,True,,2024-06-04T18:34:04Z,2024-10-19T00:08:31Z,2024-06-04T18:34:04Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/tosca-ServiceNow,https://github.com/cox-cei/tosca-ServiceNow.git
tosca-Workday,cox-cei/tosca-Workday,None,True,,2023-12-01T15:03:45Z,2024-10-18T20:47:45Z,2023-12-01T15:03:46Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/tosca-Workday,https://github.com/cox-cei/tosca-Workday.git
UiPath-PipelineTesting,cox-cei/UiPath-PipelineTesting,None,True,PowerShell,2024-03-27T15:42:39Z,2024-12-10T11:12:21Z,2024-12-11T11:30:33Z,1682,0,0,0,0,main,False,False,https://github.com/cox-cei/UiPath-PipelineTesting,https://github.com/cox-cei/UiPath-PipelineTesting.git
wordpress-brandedemptysite,cox-cei/wordpress-brandedemptysite,None,True,CSS,2024-01-17T21:25:55Z,2024-10-18T20:48:21Z,2024-01-17T21:41:57Z,5327,0,0,0,0,master,False,False,https://github.com/cox-cei/wordpress-brandedemptysite,https://github.com/cox-cei/wordpress-brandedemptysite.git
wordpressvip-coxalert,cox-cei/wordpressvip-coxalert,,True,,2023-09-15T19:05:15Z,2025-04-11T12:30:36Z,2025-04-11T12:40:20Z,10,0,0,0,0,main,False,False,https://github.com/cox-cei/wordpressvip-coxalert,https://github.com/cox-cei/wordpressvip-coxalert.git
wordpressvip-coxenterprises,cox-cei/wordpressvip-coxenterprises,None,True,PHP,2023-11-08T15:26:05Z,2025-04-11T12:42:50Z,2025-04-11T12:51:26Z,85317,0,0,0,0,main,False,False,https://github.com/cox-cei/wordpressvip-coxenterprises,https://github.com/cox-cei/wordpressvip-coxenterprises.git
wordpressvip-coxenterprises-redesign,cox-cei/wordpressvip-coxenterprises-redesign,New Repo for the newly redesigned Coxenterprises site,True,,2025-04-29T15:52:44Z,2025-04-29T15:52:46Z,2025-04-29T15:52:44Z,0,0,0,0,0,main,False,False,https://github.com/cox-cei/wordpressvip-coxenterprises-redesign,https://github.com/cox-cei/wordpressvip-coxenterprises-redesign.git
wordpressvip-coxrelief,cox-cei/wordpressvip-coxrelief,Code for Word Press site Cox Relief,True,,2023-12-06T17:20:15Z,2025-04-11T12:54:02Z,2025-04-11T12:55:33Z,5,0,0,0,0,main,False,False,https://github.com/cox-cei/wordpressvip-coxrelief,https://github.com/cox-cei/wordpressvip-coxrelief.git
Workamajig-logic-apps,cox-cei/Workamajig-logic-apps,,True,,2024-12-12T14:31:04Z,2025-04-11T12:58:33Z,2025-08-01T14:41:03Z,100,0,0,0,0,main,False,False,https://github.com/cox-cei/Workamajig-logic-apps,https://github.com/cox-cei/Workamajig-logic-apps.git
Workday-studio-integrations,cox-cei/Workday-studio-integrations,None,True,XSLT,2024-09-25T20:45:35Z,2025-07-30T18:09:40Z,2025-07-30T18:09:37Z,3034,1,1,0,0,master,False,False,https://github.com/cox-cei/Workday-studio-integrations,https://github.com/cox-cei/Workday-studio-integrations.git
workday-xslt-integrations,cox-cei/workday-xslt-integrations,Workday XSLT Source Control,True,,2024-12-09T19:04:47Z,2025-07-17T05:01:19Z,2025-07-17T05:01:17Z,18,1,1,0,0,main,False,False,https://github.com/cox-cei/workday-xslt-integrations,https://github.com/cox-cei/workday-xslt-integrations.git
workdaymiddleware_glAPI,cox-cei/workdaymiddleware_glAPI,,True,C#,2024-12-16T15:58:07Z,2025-07-01T10:33:38Z,2025-07-01T10:33:34Z,368,0,0,0,0,main,False,False,https://github.com/cox-cei/workdaymiddleware_glAPI,https://github.com/cox-cei/workdaymiddleware_glAPI.git
workdaymiddleware_glUI,cox-cei/workdaymiddleware_glUI,,True,HTML,2024-12-16T16:01:46Z,2025-06-05T15:17:11Z,2025-06-05T15:17:09Z,2178,0,0,0,0,main,False,False,https://github.com/cox-cei/workdaymiddleware_glUI,https://github.com/cox-cei/workdaymiddleware_glUI.git
