name,full_name,description,private,language,created_at,updated_at,pushed_at,size,stargazers_count,watchers_count,forks_count,open_issues_count,default_branch,archived,disabled,html_url,clone_url,ssh_url,visibility
CEI-IA-Common-Library,cox-cei/CEI-IA-Common-Library,Common utility workflows used in many automations.,True,C#,2025-06-11 14:44:28 UTC,2025-07-28 16:53:31 UTC,2025-07-28 16:53:27 UTC,18,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-IA-Common-Library,https://github.com/cox-cei/CEI-IA-Common-Library.git,**************:cox-cei/CEI-IA-Common-Library.git,private
CEI-IA-Excel-Common-Library,cox-cei/CEI-IA-Excel-Common-Library,Utility workflows for Excel used across multiple automations,True,,2025-06-11 14:43:41 UTC,2025-06-11 14:43:43 UTC,2025-06-11 14:43:41 UTC,0,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-IA-Excel-Common-Library,https://github.com/cox-cei/CEI-IA-Excel-Common-Library.git,**************:cox-cei/CEI-IA-Excel-Common-Library.git,private
CEI-IA-SharePoint-Common-Library,cox-cei/CEI-IA-SharePoint-Common-Library,Utility workflows for SharePoint used across multiple automations,True,,2025-06-11 14:44:25 UTC,2025-06-11 14:44:26 UTC,2025-06-11 14:44:25 UTC,0,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-IA-SharePoint-Common-Library,https://github.com/cox-cei/CEI-IA-SharePoint-Common-Library.git,**************:cox-cei/CEI-IA-SharePoint-Common-Library.git,private
CEI-IA-SSO-Library,cox-cei/CEI-IA-SSO-Library,Internal UiPath Library for connecting to SSO applications,True,,2025-07-01 20:01:50 UTC,2025-07-02 20:45:05 UTC,2025-07-02 21:47:31 UTC,1012,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-IA-SSO-Library,https://github.com/cox-cei/CEI-IA-SSO-Library.git,**************:cox-cei/CEI-IA-SSO-Library.git,private
CEI-Internal-Oracle-InvoiceWorkbench,cox-cei/CEI-Internal-Oracle-InvoiceWorkbench,,True,,2025-01-10 17:03:05 UTC,2025-07-17 16:05:33 UTC,2025-07-18 14:24:26 UTC,12358,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-Internal-Oracle-InvoiceWorkbench,https://github.com/cox-cei/CEI-Internal-Oracle-InvoiceWorkbench.git,**************:cox-cei/CEI-Internal-Oracle-InvoiceWorkbench.git,private
CEI-RPA-CSC_CallSummarization_PIIMaskingModel,cox-cei/CEI-RPA-CSC_CallSummarization_PIIMaskingModel,,True,,2025-04-07 20:21:38 UTC,2025-04-07 20:21:40 UTC,2025-04-07 20:21:39 UTC,0,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-RPA-CSC_CallSummarization_PIIMaskingModel,https://github.com/cox-cei/CEI-RPA-CSC_CallSummarization_PIIMaskingModel.git,**************:cox-cei/CEI-RPA-CSC_CallSummarization_PIIMaskingModel.git,private
CEI-RPA-CSC_CallSummarization_SummarizationModel,cox-cei/CEI-RPA-CSC_CallSummarization_SummarizationModel,,True,,2025-04-07 20:21:52 UTC,2025-04-07 20:21:54 UTC,2025-04-07 20:21:52 UTC,0,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-RPA-CSC_CallSummarization_SummarizationModel,https://github.com/cox-cei/CEI-RPA-CSC_CallSummarization_SummarizationModel.git,**************:cox-cei/CEI-RPA-CSC_CallSummarization_SummarizationModel.git,private
CEI-RPA-SustainabilityCloud-Dispatcher,cox-cei/CEI-RPA-SustainabilityCloud-Dispatcher,,True,,2024-12-12 14:29:40 UTC,2025-01-03 16:05:40 UTC,2025-01-03 16:05:36 UTC,1085,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Dispatcher,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Dispatcher.git,**************:cox-cei/CEI-RPA-SustainabilityCloud-Dispatcher.git,private
CEI-RPA-SustainabilityCloud-Document-Performer,cox-cei/CEI-RPA-SustainabilityCloud-Document-Performer,,True,,2024-12-12 14:30:54 UTC,2025-03-10 17:43:08 UTC,2025-03-10 17:43:04 UTC,10240,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Document-Performer,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Document-Performer.git,**************:cox-cei/CEI-RPA-SustainabilityCloud-Document-Performer.git,private
CEI-RPA-SustainabilityCloud-Output-Performer,cox-cei/CEI-RPA-SustainabilityCloud-Output-Performer,,True,Batchfile,2024-12-12 14:30:25 UTC,2025-01-03 15:52:24 UTC,2025-01-03 15:52:21 UTC,9906,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Output-Performer,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Output-Performer.git,**************:cox-cei/CEI-RPA-SustainabilityCloud-Output-Performer.git,private
CEI-RPA-SustainabilityCloud-Webcenter-Performer,cox-cei/CEI-RPA-SustainabilityCloud-Webcenter-Performer,,True,,2024-12-12 14:30:43 UTC,2025-06-26 14:13:16 UTC,2025-06-26 14:13:11 UTC,1325,0,0,0,0,main,False,False,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Webcenter-Performer,https://github.com/cox-cei/CEI-RPA-SustainabilityCloud-Webcenter-Performer.git,**************:cox-cei/CEI-RPA-SustainabilityCloud-Webcenter-Performer.git,private
COX-REFramework-Dispatcher,cox-cei/COX-REFramework-Dispatcher,A COX baked Dispatcher based off of UiPath's REFramework ,True,HTML,2024-05-15 15:25:46 UTC,2024-11-14 22:08:20 UTC,2024-11-14 22:08:17 UTC,976,0,0,0,0,main,False,False,https://github.com/cox-cei/COX-REFramework-Dispatcher,https://github.com/cox-cei/COX-REFramework-Dispatcher.git,**************:cox-cei/COX-REFramework-Dispatcher.git,private
COX-REFramework-Performer,cox-cei/COX-REFramework-Performer,A COX baked Performer based off of UiPath's REFramework,True,HTML,2024-05-15 15:25:44 UTC,2024-11-25 13:34:06 UTC,2025-03-10 20:00:59 UTC,95790,0,0,0,1,main,False,False,https://github.com/cox-cei/COX-REFramework-Performer,https://github.com/cox-cei/COX-REFramework-Performer.git,**************:cox-cei/COX-REFramework-Performer.git,private
IA-APSharedServices-Dispatcher,cox-cei/IA-APSharedServices-Dispatcher,The dispatcher part of the bot that will load emails from the AP Shared Services Shared Mailbox into a queue,True,,2024-06-21 15:08:34 UTC,2025-06-26 14:10:31 UTC,2025-06-26 14:10:32 UTC,8849,0,0,0,0,main,False,False,https://github.com/cox-cei/IA-APSharedServices-Dispatcher,https://github.com/cox-cei/IA-APSharedServices-Dispatcher.git,**************:cox-cei/IA-APSharedServices-Dispatcher.git,private
IA-APSharedServices-Performer,cox-cei/IA-APSharedServices-Performer,The performer part of the bot will take action on emails that were uploaded into the queue by the dispatcher,True,HTML,2024-06-21 15:08:32 UTC,2025-07-18 08:44:46 UTC,2025-07-18 08:44:43 UTC,8066,0,0,0,0,main,False,False,https://github.com/cox-cei/IA-APSharedServices-Performer,https://github.com/cox-cei/IA-APSharedServices-Performer.git,**************:cox-cei/IA-APSharedServices-Performer.git,private
IA-SharePointContentTagging-Dispatcher,cox-cei/IA-SharePointContentTagging-Dispatcher,A UiPath Dispatcher that traverses current content on a SharePoint site to then later tag and summarize that piece of content.,True,HTML,2024-06-17 13:33:49 UTC,2024-08-02 19:05:47 UTC,2024-11-14 21:48:32 UTC,3688,0,0,0,0,main,False,False,https://github.com/cox-cei/IA-SharePointContentTagging-Dispatcher,https://github.com/cox-cei/IA-SharePointContentTagging-Dispatcher.git,**************:cox-cei/IA-SharePointContentTagging-Dispatcher.git,private
IA-SharePointContentTagging-Performer,cox-cei/IA-SharePointContentTagging-Performer,A UiPath Performer that works the content items in the queue to tag and summarize that piece of content. Then the automation will upload that newly formed metadata into SharePoint to make the content more searchable.,True,C#,2024-06-21 14:37:46 UTC,2024-11-14 21:45:13 UTC,2024-11-14 21:45:31 UTC,95773,0,0,0,0,main,False,False,https://github.com/cox-cei/IA-SharePointContentTagging-Performer,https://github.com/cox-cei/IA-SharePointContentTagging-Performer.git,**************:cox-cei/IA-SharePointContentTagging-Performer.git,private
internal-oracle-webcenter,cox-cei/internal-oracle-webcenter,Generic activities for downloading Oracle app from web and interact with the application,True,C#,2025-04-02 14:54:14 UTC,2025-07-14 20:52:49 UTC,2025-07-25 16:17:20 UTC,34970,0,0,0,2,main,False,False,https://github.com/cox-cei/internal-oracle-webcenter,https://github.com/cox-cei/internal-oracle-webcenter.git,**************:cox-cei/internal-oracle-webcenter.git,private
MicrosoftGraph-API-Library,cox-cei/MicrosoftGraph-API-Library,None,True,,2024-09-26 21:20:14 UTC,2025-02-14 18:17:15 UTC,2025-02-14 18:23:54 UTC,396,0,0,0,0,main,False,False,https://github.com/cox-cei/MicrosoftGraph-API-Library,https://github.com/cox-cei/MicrosoftGraph-API-Library.git,**************:cox-cei/MicrosoftGraph-API-Library.git,private
PA-CEI-Finance-Tax-Diligent-Company-Profile-Performer,cox-cei/PA-CEI-Finance-Tax-Diligent-Company-Profile-Performer,,True,,2025-07-18 12:26:45 UTC,2025-07-29 16:30:39 UTC,2025-07-29 16:30:43 UTC,0,0,0,0,0,main,False,False,https://github.com/cox-cei/PA-CEI-Finance-Tax-Diligent-Company-Profile-Performer,https://github.com/cox-cei/PA-CEI-Finance-Tax-Diligent-Company-Profile-Performer.git,**************:cox-cei/PA-CEI-Finance-Tax-Diligent-Company-Profile-Performer.git,private
RDA_CAI_Finance_BlackLineReporting_BlackLineReporting,cox-cei/RDA_CAI_Finance_BlackLineReporting_BlackLineReporting,None,True,,2024-08-09 16:08:30 UTC,2024-11-27 08:13:06 UTC,2024-11-27 08:13:04 UTC,12068,0,0,0,0,main,False,False,https://github.com/cox-cei/RDA_CAI_Finance_BlackLineReporting_BlackLineReporting,https://github.com/cox-cei/RDA_CAI_Finance_BlackLineReporting_BlackLineReporting.git,**************:cox-cei/RDA_CAI_Finance_BlackLineReporting_BlackLineReporting.git,private
RDA_CAI_Retail_CMSTitleInquiry_TMSQuery,cox-cei/RDA_CAI_Retail_CMSTitleInquiry_TMSQuery,None,True,HTML,2024-08-09 16:01:57 UTC,2025-05-28 12:05:26 UTC,2025-05-28 12:05:23 UTC,10383,0,0,0,0,main,False,False,https://github.com/cox-cei/RDA_CAI_Retail_CMSTitleInquiry_TMSQuery,https://github.com/cox-cei/RDA_CAI_Retail_CMSTitleInquiry_TMSQuery.git,**************:cox-cei/RDA_CAI_Retail_CMSTitleInquiry_TMSQuery.git,private
RDA_CAI_Retail_CMSTitleInquiry_TMSUpdate,cox-cei/RDA_CAI_Retail_CMSTitleInquiry_TMSUpdate,None,True,,2024-08-09 16:03:47 UTC,2025-05-28 12:06:09 UTC,2025-05-28 12:06:06 UTC,21447,0,0,0,0,main,False,False,https://github.com/cox-cei/RDA_CAI_Retail_CMSTitleInquiry_TMSUpdate,https://github.com/cox-cei/RDA_CAI_Retail_CMSTitleInquiry_TMSUpdate.git,**************:cox-cei/RDA_CAI_Retail_CMSTitleInquiry_TMSUpdate.git,private
RDA_CEI_HR_OhioWorkersComp_OhioWorkersComp,cox-cei/RDA_CEI_HR_OhioWorkersComp_OhioWorkersComp,None,True,,2024-08-09 16:09:51 UTC,2025-03-27 18:40:48 UTC,2025-03-27 18:40:45 UTC,35972,0,0,0,0,main,False,False,https://github.com/cox-cei/RDA_CEI_HR_OhioWorkersComp_OhioWorkersComp,https://github.com/cox-cei/RDA_CEI_HR_OhioWorkersComp_OhioWorkersComp.git,**************:cox-cei/RDA_CEI_HR_OhioWorkersComp_OhioWorkersComp.git,private
RDA_CEI_IBT_Condeco_MeetingCreator,cox-cei/RDA_CEI_IBT_Condeco_MeetingCreator,None,True,JavaScript,2024-08-09 16:06:00 UTC,2025-02-07 12:12:58 UTC,2025-02-07 12:12:54 UTC,48181,0,0,0,0,main,False,False,https://github.com/cox-cei/RDA_CEI_IBT_Condeco_MeetingCreator,https://github.com/cox-cei/RDA_CEI_IBT_Condeco_MeetingCreator.git,**************:cox-cei/RDA_CEI_IBT_Condeco_MeetingCreator.git,private
RDA_CEI_IBT_Condeco_MeetingMigrator,cox-cei/RDA_CEI_IBT_Condeco_MeetingMigrator,None,True,JavaScript,2024-08-09 16:09:06 UTC,2024-11-05 16:35:48 UTC,2024-11-08 09:14:37 UTC,34892,0,0,0,0,main,False,False,https://github.com/cox-cei/RDA_CEI_IBT_Condeco_MeetingMigrator,https://github.com/cox-cei/RDA_CEI_IBT_Condeco_MeetingMigrator.git,**************:cox-cei/RDA_CEI_IBT_Condeco_MeetingMigrator.git,private
RPA-CAI-FIN-APPaymentExceptions-Dispatcher,cox-cei/RPA-CAI-FIN-APPaymentExceptions-Dispatcher,The dispatcher part of the bot that will put tickets from the ServiceStation into a queue,True,HTML,2024-07-08 21:07:38 UTC,2024-11-14 19:02:57 UTC,2025-01-09 17:11:26 UTC,3056,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-Dispatcher.git,**************:cox-cei/RPA-CAI-FIN-APPaymentExceptions-Dispatcher.git,private
RPA-CAI-FIN-APPaymentExceptions-OracleLibrary,cox-cei/RPA-CAI-FIN-APPaymentExceptions-OracleLibrary,"Library for all Oracle Screens, Ui Objects, and Descriptors",True,,2024-07-11 17:30:33 UTC,2024-12-04 13:08:30 UTC,2025-04-21 10:37:40 UTC,3762,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-OracleLibrary,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-OracleLibrary.git,**************:cox-cei/RPA-CAI-FIN-APPaymentExceptions-OracleLibrary.git,private
RPA-CAI-FIN-APPaymentExceptions-Performer,cox-cei/RPA-CAI-FIN-APPaymentExceptions-Performer,The performer part of the bot that will match tickets from the ServiceStation to Oracle and work them in Wells Fargo,True,HTML,2024-07-08 21:07:40 UTC,2025-07-16 17:16:35 UTC,2025-07-16 17:16:33 UTC,109551,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-Performer,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-Performer.git,**************:cox-cei/RPA-CAI-FIN-APPaymentExceptions-Performer.git,private
RPA-CAI-FIN-APPaymentExceptions-ServiceStationLibrary,cox-cei/RPA-CAI-FIN-APPaymentExceptions-ServiceStationLibrary,"Library for all ServiceStation Screens, Ui Objects, and Descriptors",True,,2024-07-11 17:30:35 UTC,2024-11-14 20:26:38 UTC,2024-11-14 20:26:34 UTC,43,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-ServiceStationLibrary,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-ServiceStationLibrary.git,**************:cox-cei/RPA-CAI-FIN-APPaymentExceptions-ServiceStationLibrary.git,private
RPA-CAI-FIN-APPaymentExceptions-WellsFargoLibrary,cox-cei/RPA-CAI-FIN-APPaymentExceptions-WellsFargoLibrary,"Library for all Wells Fargo Screens, Ui Objects, and Descriptors",True,,2024-07-11 17:30:37 UTC,2024-11-14 20:39:23 UTC,2025-02-27 06:46:14 UTC,8943,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-WellsFargoLibrary,https://github.com/cox-cei/RPA-CAI-FIN-APPaymentExceptions-WellsFargoLibrary.git,**************:cox-cei/RPA-CAI-FIN-APPaymentExceptions-WellsFargoLibrary.git,private
RPA-CAI-FIN-CreditMemoCAI-Dispatcher,cox-cei/RPA-CAI-FIN-CreditMemoCAI-Dispatcher,,True,HTML,2025-07-11 16:39:16 UTC,2025-07-17 13:33:13 UTC,2025-08-01 18:52:38 UTC,1758,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoCAI-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoCAI-Dispatcher.git,**************:cox-cei/RPA-CAI-FIN-CreditMemoCAI-Dispatcher.git,private
RPA-CAI-FIN-CreditMemoCAI-Performer,cox-cei/RPA-CAI-FIN-CreditMemoCAI-Performer,This is the Credit Memo CAI Performer,True,,2025-07-10 13:17:42 UTC,2025-07-28 18:15:53 UTC,2025-08-01 18:55:47 UTC,5145,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoCAI-Performer,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoCAI-Performer.git,**************:cox-cei/RPA-CAI-FIN-CreditMemoCAI-Performer.git,private
RPA-CAI-FIN-CreditMemoIS-Dispatcher,cox-cei/RPA-CAI-FIN-CreditMemoIS-Dispatcher,,True,HTML,2025-07-11 16:39:33 UTC,2025-07-30 14:55:34 UTC,2025-08-01 18:52:49 UTC,1868,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoIS-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoIS-Dispatcher.git,**************:cox-cei/RPA-CAI-FIN-CreditMemoIS-Dispatcher.git,private
RPA-CAI-FIN-CreditMemoIS-Performer,cox-cei/RPA-CAI-FIN-CreditMemoIS-Performer,This is the Credit Memo IS Performer,True,PowerShell,2025-07-10 14:56:35 UTC,2025-07-31 19:56:32 UTC,2025-08-02 20:54:30 UTC,5243,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoIS-Performer,https://github.com/cox-cei/RPA-CAI-FIN-CreditMemoIS-Performer.git,**************:cox-cei/RPA-CAI-FIN-CreditMemoIS-Performer.git,private
RPA-CAI-FIN-Disbursements-Dispatcher,cox-cei/RPA-CAI-FIN-Disbursements-Dispatcher,None,True,HTML,2024-08-29 14:23:32 UTC,2025-06-20 15:15:59 UTC,2025-06-20 15:15:56 UTC,1864,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Dispatcher.git,**************:cox-cei/RPA-CAI-FIN-Disbursements-Dispatcher.git,private
RPA-CAI-FIN-Disbursements-Oracle,cox-cei/RPA-CAI-FIN-Disbursements-Oracle,None,True,C#,2024-08-29 14:23:26 UTC,2025-01-09 16:47:57 UTC,2025-01-09 16:47:50 UTC,6808,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Oracle,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Oracle.git,**************:cox-cei/RPA-CAI-FIN-Disbursements-Oracle.git,private
RPA-CAI-FIN-Disbursements-Performer,cox-cei/RPA-CAI-FIN-Disbursements-Performer,None,True,C#,2024-08-29 14:23:28 UTC,2025-06-20 15:14:44 UTC,2025-06-20 15:14:40 UTC,112863,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Performer,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Performer.git,**************:cox-cei/RPA-CAI-FIN-Disbursements-Performer.git,private
RPA-CAI-FIN-Disbursements-Performer-2,cox-cei/RPA-CAI-FIN-Disbursements-Performer-2,,True,HTML,2024-12-09 16:47:46 UTC,2025-06-20 15:15:21 UTC,2025-06-27 10:46:04 UTC,111733,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Performer-2,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Performer-2.git,**************:cox-cei/RPA-CAI-FIN-Disbursements-Performer-2.git,private
RPA-CAI-FIN-Disbursements-Synergy,cox-cei/RPA-CAI-FIN-Disbursements-Synergy,None,True,,2024-08-29 14:23:33 UTC,2025-06-20 15:08:46 UTC,2025-06-20 15:08:42 UTC,8052,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Synergy,https://github.com/cox-cei/RPA-CAI-FIN-Disbursements-Synergy.git,**************:cox-cei/RPA-CAI-FIN-Disbursements-Synergy.git,private
RPA-CAI-FIN-EAPSharedEmails-Dispatcher,cox-cei/RPA-CAI-FIN-EAPSharedEmails-Dispatcher,,True,HTML,2025-01-27 21:43:27 UTC,2025-02-05 16:13:19 UTC,2025-07-15 19:41:55 UTC,1491,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-EAPSharedEmails-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-EAPSharedEmails-Dispatcher.git,**************:cox-cei/RPA-CAI-FIN-EAPSharedEmails-Dispatcher.git,private
RPA-CAI-FIN-EAPSharedEmails-Performer,cox-cei/RPA-CAI-FIN-EAPSharedEmails-Performer,,True,HTML,2025-01-27 21:42:31 UTC,2025-02-04 15:41:31 UTC,2025-07-16 20:04:26 UTC,95709,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-EAPSharedEmails-Performer,https://github.com/cox-cei/RPA-CAI-FIN-EAPSharedEmails-Performer.git,**************:cox-cei/RPA-CAI-FIN-EAPSharedEmails-Performer.git,private
RPA-CAI-FIN-FS-EmailApprovedROs-Performer,cox-cei/RPA-CAI-FIN-FS-EmailApprovedROs-Performer,,True,HTML,2025-06-10 15:28:35 UTC,2025-06-10 17:55:38 UTC,2025-07-09 14:22:55 UTC,2130,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-FS-EmailApprovedROs-Performer,https://github.com/cox-cei/RPA-CAI-FIN-FS-EmailApprovedROs-Performer.git,**************:cox-cei/RPA-CAI-FIN-FS-EmailApprovedROs-Performer.git,private
RPA-CAI-FIN-FS-EstimationApprovals-AutoIntegrate-Performer,cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-AutoIntegrate-Performer,,True,HTML,2025-06-10 15:43:44 UTC,2025-06-10 16:26:38 UTC,2025-07-07 15:02:27 UTC,1326,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-AutoIntegrate-Performer,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-AutoIntegrate-Performer.git,**************:cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-AutoIntegrate-Performer.git,private
RPA-CAI-FIN-FS-EstimationApprovals-Dispatcher,cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-Dispatcher,,True,HTML,2025-06-10 15:43:47 UTC,2025-06-30 18:46:21 UTC,2025-07-18 19:04:24 UTC,1019,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-Dispatcher.git,**************:cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-Dispatcher.git,private
RPA-CAI-FIN-FS-EstimationApprovals-Holman-Performer,cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-Holman-Performer,,True,HTML,2025-06-10 15:28:48 UTC,2025-06-30 19:21:16 UTC,2025-07-02 15:09:41 UTC,1373,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-Holman-Performer,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-Holman-Performer.git,**************:cox-cei/RPA-CAI-FIN-FS-EstimationApprovals-Holman-Performer.git,private
RPA-CAI-FIN-FS-EstimationBilling-AutoIntegrate-Performer,cox-cei/RPA-CAI-FIN-FS-EstimationBilling-AutoIntegrate-Performer,fleet services estimation billing,True,HTML,2025-07-08 18:45:24 UTC,2025-07-10 18:55:58 UTC,2025-07-30 15:26:53 UTC,2337,0,0,0,1,dev,False,False,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationBilling-AutoIntegrate-Performer,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationBilling-AutoIntegrate-Performer.git,**************:cox-cei/RPA-CAI-FIN-FS-EstimationBilling-AutoIntegrate-Performer.git,private
RPA-CAI-FIN-FS-EstimationBilling-Dispatcher,cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Dispatcher,,True,HTML,2025-07-02 12:39:03 UTC,2025-07-28 19:09:49 UTC,2025-07-28 19:09:47 UTC,2216,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Dispatcher.git,**************:cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Dispatcher.git,private
RPA-CAI-FIN-FS-EstimationBilling-Holman-Performer,cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Holman-Performer,fleet services estimation billing,True,HTML,2025-07-08 18:35:31 UTC,2025-07-09 19:47:51 UTC,2025-07-31 18:42:33 UTC,2319,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Holman-Performer,https://github.com/cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Holman-Performer.git,**************:cox-cei/RPA-CAI-FIN-FS-EstimationBilling-Holman-Performer.git,private
RPA-CAI-FIN-InvoiceCancellation-Dispatcher,cox-cei/RPA-CAI-FIN-InvoiceCancellation-Dispatcher,This is the Dispatcher for Invoice Cancellations,True,HTML,2025-04-08 15:28:53 UTC,2025-07-24 20:34:58 UTC,2025-07-24 20:34:56 UTC,1007,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-InvoiceCancellation-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-InvoiceCancellation-Dispatcher.git,**************:cox-cei/RPA-CAI-FIN-InvoiceCancellation-Dispatcher.git,private
RPA-CAI-FIN-InvoiceCancellation-Performer,cox-cei/RPA-CAI-FIN-InvoiceCancellation-Performer,Performer bot for the Invoice Cancellation process. This project handles the Oracle invoice cancellation steps and appropriate updates servicenow tickets appropriately,True,HTML,2025-03-31 14:26:57 UTC,2025-04-08 15:18:06 UTC,2025-07-23 18:16:22 UTC,2727,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-InvoiceCancellation-Performer,https://github.com/cox-cei/RPA-CAI-FIN-InvoiceCancellation-Performer.git,**************:cox-cei/RPA-CAI-FIN-InvoiceCancellation-Performer.git,private
RPA-CAI-FIN-Karmak-addunits-library,cox-cei/RPA-CAI-FIN-Karmak-addunits-library,Library containing object repository and custom activities for interacting with the Karmak application. Used in the Karmak Add Units automation.,True,,2025-02-26 17:53:40 UTC,2025-02-26 18:22:01 UTC,2025-04-22 17:41:40 UTC,5128,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-addunits-library,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-addunits-library.git,**************:cox-cei/RPA-CAI-FIN-Karmak-addunits-library.git,private
RPA-CAI-FIN-Karmak-AddUnits-Performer,cox-cei/RPA-CAI-FIN-Karmak-AddUnits-Performer,Automatiopn used to input VIN given in transaction item into VINDecoder website and retrieve vehicle details. The bot then uses the vehicle details to add new units in the Karmak application.,True,HTML,2025-02-12 18:32:18 UTC,2025-07-24 16:09:38 UTC,2025-07-24 16:09:35 UTC,2976,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-AddUnits-Performer,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-AddUnits-Performer.git,**************:cox-cei/RPA-CAI-FIN-Karmak-AddUnits-Performer.git,private
RPA-CAI-FIN-Karmak-Amazon-Dispatcher,cox-cei/RPA-CAI-FIN-Karmak-Amazon-Dispatcher,,True,HTML,2025-04-15 14:34:58 UTC,2025-06-06 17:40:29 UTC,2025-07-30 15:41:17 UTC,1506,0,0,0,2,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-Amazon-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-Amazon-Dispatcher.git,**************:cox-cei/RPA-CAI-FIN-Karmak-Amazon-Dispatcher.git,private
RPA-CAI-FIN-Karmak-Amazon-Performer,cox-cei/RPA-CAI-FIN-Karmak-Amazon-Performer,,True,HTML,2025-04-15 14:34:32 UTC,2025-07-01 14:29:28 UTC,2025-07-01 14:51:05 UTC,5121,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-Amazon-Performer,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-Amazon-Performer.git,**************:cox-cei/RPA-CAI-FIN-Karmak-Amazon-Performer.git,private
RPA-CAI-FIN-Karmak-EstimationApprovals-Dispatcher,cox-cei/RPA-CAI-FIN-Karmak-EstimationApprovals-Dispatcher,,True,,2025-04-15 14:35:37 UTC,2025-05-12 14:23:35 UTC,2025-06-23 19:33:47 UTC,1104,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-EstimationApprovals-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-EstimationApprovals-Dispatcher.git,**************:cox-cei/RPA-CAI-FIN-Karmak-EstimationApprovals-Dispatcher.git,private
RPA-CAI-FIN-Karmak-EstimationApprovals-Performer,cox-cei/RPA-CAI-FIN-Karmak-EstimationApprovals-Performer,,True,HTML,2025-04-15 14:35:13 UTC,2025-05-28 16:15:33 UTC,2025-06-23 19:04:55 UTC,1602,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-EstimationApprovals-Performer,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-EstimationApprovals-Performer.git,**************:cox-cei/RPA-CAI-FIN-Karmak-EstimationApprovals-Performer.git,private
RPA-CAI-FIN-Karmak-FileDispatcher,cox-cei/RPA-CAI-FIN-Karmak-FileDispatcher,Process used to loop through files in the New folder of CAI sharepoint and capture the file details as transaction items in our queue,True,HTML,2025-02-12 18:26:36 UTC,2025-02-14 15:26:37 UTC,2025-05-05 20:44:58 UTC,979,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-FileDispatcher,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-FileDispatcher.git,**************:cox-cei/RPA-CAI-FIN-Karmak-FileDispatcher.git,private
RPA-CAI-FIN-Karmak-VIN-Dispatcher,cox-cei/RPA-CAI-FIN-Karmak-VIN-Dispatcher,Process used to ingest the sharepoint files which the File Dispatcher created as queue items. The bot will read through the given excel files and create a transaction item for each row in the file,True,HTML,2025-02-11 18:41:59 UTC,2025-05-06 15:05:22 UTC,2025-05-06 15:05:19 UTC,1039,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-VIN-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-VIN-Dispatcher.git,**************:cox-cei/RPA-CAI-FIN-Karmak-VIN-Dispatcher.git,private
RPA-CAI-FIN-Karmak-VINDecoder-Library,cox-cei/RPA-CAI-FIN-Karmak-VINDecoder-Library,Object repository and custom activity library for navigating to and interacting with www.decodethevin.com,True,C#,2025-02-12 18:32:30 UTC,2025-02-17 20:11:51 UTC,2025-07-29 17:22:22 UTC,5103,0,0,0,2,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-VINDecoder-Library,https://github.com/cox-cei/RPA-CAI-FIN-Karmak-VINDecoder-Library.git,**************:cox-cei/RPA-CAI-FIN-Karmak-VINDecoder-Library.git,private
RPA-CAI-FIN-ManheimRedemption-Dispatcher,cox-cei/RPA-CAI-FIN-ManheimRedemption-Dispatcher,,True,HTML,2025-07-24 13:32:28 UTC,2025-07-24 16:54:50 UTC,2025-07-24 16:54:46 UTC,970,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-ManheimRedemption-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-ManheimRedemption-Dispatcher.git,**************:cox-cei/RPA-CAI-FIN-ManheimRedemption-Dispatcher.git,private
RPA-CAI-FIN-ManheimRedemption-performer,cox-cei/RPA-CAI-FIN-ManheimRedemption-performer,Performer bot for Manheim Redemption Automation,True,,2025-07-24 14:08:45 UTC,2025-07-25 15:42:29 UTC,2025-07-25 15:42:25 UTC,8160,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-ManheimRedemption-performer,https://github.com/cox-cei/RPA-CAI-FIN-ManheimRedemption-performer.git,**************:cox-cei/RPA-CAI-FIN-ManheimRedemption-performer.git,private
RPA-CAI-FIN-Rush-ConfirmPaymentPerformer,cox-cei/RPA-CAI-FIN-Rush-ConfirmPaymentPerformer,,True,HTML,2024-12-06 19:35:54 UTC,2025-07-02 14:43:55 UTC,2025-07-02 14:43:51 UTC,1995,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Rush-ConfirmPaymentPerformer,https://github.com/cox-cei/RPA-CAI-FIN-Rush-ConfirmPaymentPerformer.git,**************:cox-cei/RPA-CAI-FIN-Rush-ConfirmPaymentPerformer.git,private
RPA-CAI-FIN-Rush-Dispatcher,cox-cei/RPA-CAI-FIN-Rush-Dispatcher,,True,HTML,2024-10-22 15:16:25 UTC,2025-02-05 13:11:47 UTC,2025-05-14 14:51:51 UTC,9840,0,0,0,0,dev,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Dispatcher,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Dispatcher.git,**************:cox-cei/RPA-CAI-FIN-Rush-Dispatcher.git,private
RPA-CAI-FIN-Rush-Oracle,cox-cei/RPA-CAI-FIN-Rush-Oracle,,True,,2024-10-22 15:21:37 UTC,2024-11-19 13:20:08 UTC,2025-03-26 15:33:57 UTC,16170,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Oracle,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Oracle.git,**************:cox-cei/RPA-CAI-FIN-Rush-Oracle.git,private
RPA-CAI-FIN-Rush-Performer,cox-cei/RPA-CAI-FIN-Rush-Performer,,True,HTML,2024-10-22 15:19:41 UTC,2025-06-23 15:08:48 UTC,2025-07-21 17:58:47 UTC,112647,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Performer,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Performer.git,**************:cox-cei/RPA-CAI-FIN-Rush-Performer.git,private
RPA-CAI-FIN-Rush-Performer-2,cox-cei/RPA-CAI-FIN-Rush-Performer-2,,True,,2025-01-10 17:06:53 UTC,2025-01-10 17:06:55 UTC,2025-01-10 17:06:53 UTC,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Performer-2,https://github.com/cox-cei/RPA-CAI-FIN-Rush-Performer-2.git,**************:cox-cei/RPA-CAI-FIN-Rush-Performer-2.git,private
RPA-CAI-FIN-Rush-ServiceNowLibrary,cox-cei/RPA-CAI-FIN-Rush-ServiceNowLibrary,Library containing prebuilt API activities used to get and patch ServiceNow backend API,True,,2024-12-03 16:41:09 UTC,2025-02-03 16:22:47 UTC,2025-03-05 19:25:14 UTC,165,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FIN-Rush-ServiceNowLibrary,https://github.com/cox-cei/RPA-CAI-FIN-Rush-ServiceNowLibrary.git,**************:cox-cei/RPA-CAI-FIN-Rush-ServiceNowLibrary.git,private
RPA-CAI-FS-AutoIntegrate-Library,cox-cei/RPA-CAI-FS-AutoIntegrate-Library,Object Repository Library for AutoIntegrate Portal for FS Estimation Approvals,True,,2025-05-09 12:45:13 UTC,2025-05-19 19:09:01 UTC,2025-07-16 15:13:28 UTC,2249,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FS-AutoIntegrate-Library,https://github.com/cox-cei/RPA-CAI-FS-AutoIntegrate-Library.git,**************:cox-cei/RPA-CAI-FS-AutoIntegrate-Library.git,private
RPA-CAI-FS-Holman-Library,cox-cei/RPA-CAI-FS-Holman-Library,Object Repository Library for Holman Portal for FS Estimation Approvals,True,,2025-05-09 12:37:55 UTC,2025-05-14 15:04:33 UTC,2025-07-09 15:55:36 UTC,1338,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CAI-FS-Holman-Library,https://github.com/cox-cei/RPA-CAI-FS-Holman-Library.git,**************:cox-cei/RPA-CAI-FS-Holman-Library.git,private
RPA-CAI-FS-PreBiller-Dispatcher,cox-cei/RPA-CAI-FS-PreBiller-Dispatcher,RPA-CAI-FS-PreBiller-Dispatcher,True,HTML,2025-07-24 14:00:33 UTC,2025-07-25 15:17:47 UTC,2025-07-25 15:17:42 UTC,1589,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-FS-PreBiller-Dispatcher,https://github.com/cox-cei/RPA-CAI-FS-PreBiller-Dispatcher.git,**************:cox-cei/RPA-CAI-FS-PreBiller-Dispatcher.git,private
RPA-CAI-FS-PreBiller-Performer,cox-cei/RPA-CAI-FS-PreBiller-Performer,Fleet services Pre-Filler Performer,True,HTML,2025-07-24 13:56:32 UTC,2025-07-25 20:29:11 UTC,2025-08-01 18:46:21 UTC,3467,0,0,0,2,main,False,False,https://github.com/cox-cei/RPA-CAI-FS-PreBiller-Performer,https://github.com/cox-cei/RPA-CAI-FS-PreBiller-Performer.git,**************:cox-cei/RPA-CAI-FS-PreBiller-Performer.git,private
RPA-CAI-Manheim-VinReconciliationReportingPerformer,cox-cei/RPA-CAI-Manheim-VinReconciliationReportingPerformer,Manheim Vin Reconciliation reporting automation ,True,,2025-04-18 14:27:22 UTC,2025-04-18 14:27:24 UTC,2025-04-18 14:27:23 UTC,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-Manheim-VinReconciliationReportingPerformer,https://github.com/cox-cei/RPA-CAI-Manheim-VinReconciliationReportingPerformer.git,**************:cox-cei/RPA-CAI-Manheim-VinReconciliationReportingPerformer.git,private
RPA-CAI-VinSolutions-DealerOnboarding-AdditionalConfiguration-Performer,cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-AdditionalConfiguration-Performer,This is the second performer for the Vinsolutions DealerOnboarding Process that handles the additional configuration of the dealer after the dealer instance has already been created,True,HTML,2025-04-08 15:28:30 UTC,2025-06-09 17:41:54 UTC,2025-07-30 15:07:19 UTC,8382,0,0,0,3,main,False,False,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-AdditionalConfiguration-Performer,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-AdditionalConfiguration-Performer.git,**************:cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-AdditionalConfiguration-Performer.git,private
RPA-CAI-VinSolutions-DealerOnboarding-Dispatcher,cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Dispatcher,A UiPath dispatcher process that reads onboarding emails for VinSolutions and populates a queue for processing.,True,HTML,2024-10-02 15:41:36 UTC,2025-01-16 15:03:21 UTC,2025-01-16 15:03:22 UTC,1574,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Dispatcher,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Dispatcher.git,**************:cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Dispatcher.git,private
RPA-CAI-VinSolutions-DealerOnboarding-Performer,cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Performer,A UiPath performer that processes onboarding requests for new dealers on VinSolutions.,True,HTML,2024-10-02 15:41:38 UTC,2025-05-21 16:22:49 UTC,2025-08-01 20:44:59 UTC,12120,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Performer,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Performer.git,**************:cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Performer.git,private
RPA-CAI-VinSolutions-DealerOnboarding-Reporter,cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Reporter,A UiPath reporter process that generates a report based on onboarding requests for new dealers on VinSolutions.,True,HTML,2024-10-16 18:27:45 UTC,2025-06-02 12:28:28 UTC,2025-06-02 12:28:25 UTC,2071,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Reporter,https://github.com/cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Reporter.git,**************:cox-cei/RPA-CAI-VinSolutions-DealerOnboarding-Reporter.git,private
RPA-CAI-VinSolutions-Descriptors,cox-cei/RPA-CAI-VinSolutions-Descriptors,Descriptors library for VinSolutions,True,,2025-05-30 13:14:44 UTC,2025-07-30 14:00:52 UTC,2025-08-01 17:53:47 UTC,4539,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-VinSolutions-Descriptors,https://github.com/cox-cei/RPA-CAI-VinSolutions-Descriptors.git,**************:cox-cei/RPA-CAI-VinSolutions-Descriptors.git,private
RPA-CAI-VinSolutions-FeatureEnablement-Dispatcher,cox-cei/RPA-CAI-VinSolutions-FeatureEnablement-Dispatcher,Dispatcher subprocess of RPA-CAI-VinSolutions-FeatureEnablement,True,,2025-06-16 14:15:54 UTC,2025-06-16 20:09:11 UTC,2025-07-31 18:32:55 UTC,1363,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-VinSolutions-FeatureEnablement-Dispatcher,https://github.com/cox-cei/RPA-CAI-VinSolutions-FeatureEnablement-Dispatcher.git,**************:cox-cei/RPA-CAI-VinSolutions-FeatureEnablement-Dispatcher.git,private
RPA-CAI-VinSolutions-FeatureEnablement-Performer,cox-cei/RPA-CAI-VinSolutions-FeatureEnablement-Performer,Performer subprocess of RPA-CAI-VinSolutions-FeatureEnablement,True,,2025-06-16 14:16:21 UTC,2025-06-17 17:57:33 UTC,2025-07-31 17:25:48 UTC,2525,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CAI-VinSolutions-FeatureEnablement-Performer,https://github.com/cox-cei/RPA-CAI-VinSolutions-FeatureEnablement-Performer.git,**************:cox-cei/RPA-CAI-VinSolutions-FeatureEnablement-Performer.git,private
RPA-CCI-PDC-CERTIFICATE,cox-cei/RPA-CCI-PDC-CERTIFICATE,Generate PDC Certtificate using Certify Tax,True,HTML,2025-05-12 18:58:02 UTC,2025-05-14 20:47:18 UTC,2025-07-22 12:33:53 UTC,3225,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CCI-PDC-CERTIFICATE,https://github.com/cox-cei/RPA-CCI-PDC-CERTIFICATE.git,**************:cox-cei/RPA-CCI-PDC-CERTIFICATE.git,private
RPA-CCI-PDC-CERTIFICATE-REPORTER,cox-cei/RPA-CCI-PDC-CERTIFICATE-REPORTER,Reporter subprocess for the RPA-CCI-PDC-CERTIFICATE process,True,HTML,2025-05-22 18:17:22 UTC,2025-05-22 19:55:15 UTC,2025-06-02 19:59:35 UTC,1052,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CCI-PDC-CERTIFICATE-REPORTER,https://github.com/cox-cei/RPA-CCI-PDC-CERTIFICATE-REPORTER.git,**************:cox-cei/RPA-CCI-PDC-CERTIFICATE-REPORTER.git,private
RPA-CEI-Azure-Document-Understanding-Poc,cox-cei/RPA-CEI-Azure-Document-Understanding-Poc,,True,,2025-01-29 20:56:26 UTC,2025-01-29 20:56:28 UTC,2025-01-29 20:56:27 UTC,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Azure-Document-Understanding-Poc,https://github.com/cox-cei/RPA-CEI-Azure-Document-Understanding-Poc.git,**************:cox-cei/RPA-CEI-Azure-Document-Understanding-Poc.git,private
RPA-CEI-eDiscovery-Legal-OnitUpdate-Performer,cox-cei/RPA-CEI-eDiscovery-Legal-OnitUpdate-Performer,A UiPath Performer for updating the Onit Resolution Details and Case ID by making a singular Api call. It gets the atom ID and Case ID from the Queue.,True,HTML,2024-09-17 18:59:59 UTC,2025-02-05 15:15:50 UTC,2025-02-05 15:15:46 UTC,1092,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-eDiscovery-Legal-OnitUpdate-Performer,https://github.com/cox-cei/RPA-CEI-eDiscovery-Legal-OnitUpdate-Performer.git,**************:cox-cei/RPA-CEI-eDiscovery-Legal-OnitUpdate-Performer.git,private
RPA-CEI-FIN-VentivUserManagement-Dispatcher,cox-cei/RPA-CEI-FIN-VentivUserManagement-Dispatcher,None,True,,2024-07-11 20:42:19 UTC,2024-12-10 20:59:05 UTC,2024-12-10 20:59:01 UTC,17332,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-FIN-VentivUserManagement-Dispatcher,https://github.com/cox-cei/RPA-CEI-FIN-VentivUserManagement-Dispatcher.git,**************:cox-cei/RPA-CEI-FIN-VentivUserManagement-Dispatcher.git,private
RPA-CEI-FIN-VentivUserManagement-LoginLibrary,cox-cei/RPA-CEI-FIN-VentivUserManagement-LoginLibrary,None,True,,2024-07-11 20:42:15 UTC,2024-12-10 16:45:31 UTC,2024-12-10 16:45:28 UTC,1135,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-FIN-VentivUserManagement-LoginLibrary,https://github.com/cox-cei/RPA-CEI-FIN-VentivUserManagement-LoginLibrary.git,**************:cox-cei/RPA-CEI-FIN-VentivUserManagement-LoginLibrary.git,private
RPA-CEI-FIN-VentivUserManagement-Performer,cox-cei/RPA-CEI-FIN-VentivUserManagement-Performer,None,True,HTML,2024-07-11 20:42:17 UTC,2024-11-08 21:52:13 UTC,2025-01-06 20:46:23 UTC,11099,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-FIN-VentivUserManagement-Performer,https://github.com/cox-cei/RPA-CEI-FIN-VentivUserManagement-Performer.git,**************:cox-cei/RPA-CEI-FIN-VentivUserManagement-Performer.git,private
RPA-CEI-Finance-Diligent-UI-Library,cox-cei/RPA-CEI-Finance-Diligent-UI-Library,Library to be used by the Diligent Bots,True,,2025-07-18 15:24:17 UTC,2025-07-29 16:29:31 UTC,2025-08-01 20:58:53 UTC,1434,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Diligent-UI-Library,https://github.com/cox-cei/RPA-CEI-Finance-Diligent-UI-Library.git,**************:cox-cei/RPA-CEI-Finance-Diligent-UI-Library.git,private
RPA-CEI-Finance-Tax-Diligent-Company-Profile-Dispatcher,cox-cei/RPA-CEI-Finance-Tax-Diligent-Company-Profile-Dispatcher,,True,,2025-07-18 12:27:03 UTC,2025-07-29 16:30:07 UTC,2025-07-29 16:30:17 UTC,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Diligent-Company-Profile-Dispatcher,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Diligent-Company-Profile-Dispatcher.git,**************:cox-cei/RPA-CEI-Finance-Tax-Diligent-Company-Profile-Dispatcher.git,private
RPA-CEI-Finance-Tax-Diligent-Unitary-Dispatcher,cox-cei/RPA-CEI-Finance-Tax-Diligent-Unitary-Dispatcher,,True,,2025-07-18 12:24:53 UTC,2025-07-18 12:24:55 UTC,2025-07-18 12:24:54 UTC,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Diligent-Unitary-Dispatcher,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Diligent-Unitary-Dispatcher.git,**************:cox-cei/RPA-CEI-Finance-Tax-Diligent-Unitary-Dispatcher.git,private
RPA-CEI-Finance-Tax-Diligent-Unitary-Performer,cox-cei/RPA-CEI-Finance-Tax-Diligent-Unitary-Performer,,True,,2025-07-18 12:24:27 UTC,2025-07-18 12:24:29 UTC,2025-07-18 12:24:28 UTC,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Diligent-Unitary-Performer,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Diligent-Unitary-Performer.git,**************:cox-cei/RPA-CEI-Finance-Tax-Diligent-Unitary-Performer.git,private
RPA-CEI-Finance-Tax-InvoiceRetrieval-Dispatcher,cox-cei/RPA-CEI-Finance-Tax-InvoiceRetrieval-Dispatcher,Dispatcher process for the Invoice Retrieval automation.,True,HTML,2025-06-04 20:35:56 UTC,2025-07-24 17:56:59 UTC,2025-07-31 15:12:20 UTC,1022,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-InvoiceRetrieval-Dispatcher,https://github.com/cox-cei/RPA-CEI-Finance-Tax-InvoiceRetrieval-Dispatcher.git,**************:cox-cei/RPA-CEI-Finance-Tax-InvoiceRetrieval-Dispatcher.git,private
RPA-CEI-Finance-Tax-InvoiceRetrieval-Performer,cox-cei/RPA-CEI-Finance-Tax-InvoiceRetrieval-Performer,Performer process for the Invoice Retrieval automation.,True,,2025-06-04 20:36:12 UTC,2025-06-05 12:57:21 UTC,2025-07-31 17:52:53 UTC,2236,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-InvoiceRetrieval-Performer,https://github.com/cox-cei/RPA-CEI-Finance-Tax-InvoiceRetrieval-Performer.git,**************:cox-cei/RPA-CEI-Finance-Tax-InvoiceRetrieval-Performer.git,private
RPA-CEI-Finance-Tax-NOLanVA-Dispatcher,cox-cei/RPA-CEI-Finance-Tax-NOLanVA-Dispatcher,Dispatcher process for the NOLan VA automation,True,,2025-07-08 13:38:57 UTC,2025-07-11 19:12:49 UTC,2025-07-29 12:37:34 UTC,964,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-NOLanVA-Dispatcher,https://github.com/cox-cei/RPA-CEI-Finance-Tax-NOLanVA-Dispatcher.git,**************:cox-cei/RPA-CEI-Finance-Tax-NOLanVA-Dispatcher.git,private
RPA-CEI-Finance-Tax-NOLanVA-Performer,cox-cei/RPA-CEI-Finance-Tax-NOLanVA-Performer,Performer process for the NOLan VA automation,True,,2025-07-08 13:32:22 UTC,2025-07-11 19:13:10 UTC,2025-08-01 14:32:40 UTC,1772,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-NOLanVA-Performer,https://github.com/cox-cei/RPA-CEI-Finance-Tax-NOLanVA-Performer.git,**************:cox-cei/RPA-CEI-Finance-Tax-NOLanVA-Performer.git,private
RPA-CEI-Finance-Tax-Reporter,cox-cei/RPA-CEI-Finance-Tax-Reporter,Reporting process used for the CEI Tax automations.,True,,2025-06-19 17:30:16 UTC,2025-06-24 13:49:58 UTC,2025-07-31 17:50:20 UTC,1055,0,0,0,1,main,False,False,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Reporter,https://github.com/cox-cei/RPA-CEI-Finance-Tax-Reporter.git,**************:cox-cei/RPA-CEI-Finance-Tax-Reporter.git,private
RPA-CEI-IA-CoxQueueReporter-Library,cox-cei/RPA-CEI-IA-CoxQueueReporter-Library,Library used for the IA Queue Reporter,True,,2025-06-16 14:15:43 UTC,2025-06-16 14:15:45 UTC,2025-06-16 14:15:44 UTC,0,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-IA-CoxQueueReporter-Library,https://github.com/cox-cei/RPA-CEI-IA-CoxQueueReporter-Library.git,**************:cox-cei/RPA-CEI-IA-CoxQueueReporter-Library.git,private
RPA-CEI-IA-Support-PasswordManagement-Dispatcher,cox-cei/RPA-CEI-IA-Support-PasswordManagement-Dispatcher,,True,HTML,2025-07-16 15:51:16 UTC,2025-07-21 17:05:11 UTC,2025-07-22 14:21:17 UTC,1096,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-IA-Support-PasswordManagement-Dispatcher,https://github.com/cox-cei/RPA-CEI-IA-Support-PasswordManagement-Dispatcher.git,**************:cox-cei/RPA-CEI-IA-Support-PasswordManagement-Dispatcher.git,private
RPA-CEI-IA-Support-PasswordManagement-ExpirationEmailCleanUp,cox-cei/RPA-CEI-IA-Support-PasswordManagement-ExpirationEmailCleanUp,Password Management 2nd Performer to look for all open action center items that need to be emailed out to Infrastructure Team that morning,True,HTML,2025-07-21 14:48:58 UTC,2025-07-25 18:58:41 UTC,2025-07-29 13:36:10 UTC,1206,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-IA-Support-PasswordManagement-ExpirationEmailCleanUp,https://github.com/cox-cei/RPA-CEI-IA-Support-PasswordManagement-ExpirationEmailCleanUp.git,**************:cox-cei/RPA-CEI-IA-Support-PasswordManagement-ExpirationEmailCleanUp.git,private
RPA-CEI-IA-Support-PasswordManagement-Performer,cox-cei/RPA-CEI-IA-Support-PasswordManagement-Performer,,True,HTML,2025-06-10 17:15:24 UTC,2025-07-14 15:31:17 UTC,2025-07-23 15:07:01 UTC,1514,0,0,0,0,main,False,False,https://github.com/cox-cei/RPA-CEI-IA-Support-PasswordManagement-Performer,https://github.com/cox-cei/RPA-CEI-IA-Support-PasswordManagement-Performer.git,**************:cox-cei/RPA-CEI-IA-Support-PasswordManagement-Performer.git,private
