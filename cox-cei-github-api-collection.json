{"info": {"name": "Cox-CEI GitHub Team Repositories", "description": "GitHub API collection for listing team repositories in cox-cei organization", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{github_token}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "https://api.github.com", "type": "string"}, {"key": "org", "value": "cox-cei", "type": "string"}, {"key": "team_slug", "value": "your-team-name", "type": "string"}], "item": [{"name": "Organization Info", "item": [{"name": "Get Organization", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orgs/{{org}}", "host": ["{{baseUrl}}"], "path": ["orgs", "{{org}}"]}}}, {"name": "List All Org Repositories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orgs/{{org}}/repos?per_page=100&sort=name", "host": ["{{baseUrl}}"], "path": ["orgs", "{{org}}", "repos"], "query": [{"key": "per_page", "value": "100"}, {"key": "sort", "value": "name"}]}}}]}, {"name": "Teams", "item": [{"name": "List Organization Teams", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orgs/{{org}}/teams", "host": ["{{baseUrl}}"], "path": ["orgs", "{{org}}", "teams"]}}}, {"name": "Get Team by Slug", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orgs/{{org}}/teams/{{team_slug}}", "host": ["{{baseUrl}}"], "path": ["orgs", "{{org}}", "teams", "{{team_slug}}"]}}}, {"name": "List Team Repositories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orgs/{{org}}/teams/{{team_slug}}/repos?per_page=100", "host": ["{{baseUrl}}"], "path": ["orgs", "{{org}}", "teams", "{{team_slug}}", "repos"], "query": [{"key": "per_page", "value": "100"}]}}, "event": [{"listen": "test", "script": {"exec": ["// Extract repository names and create comma-separated list", "const response = pm.response.json();", "const repoNames = response.map(repo => repo.name);", "const csvList = repoNames.join(', ');", "", "console.log('Repository Names (CSV):');", "console.log(csvList);", "", "// Set as environment variable for easy access", "pm.environment.set('team_repos_csv', csvList);", "", "// Also log individual repo details", "console.log('\\nDetailed Repository List:');", "response.forEach(repo => {", "    console.log(`${repo.name} - ${repo.html_url} (${repo.private ? 'Private' : 'Public'})`);", "});", "", "pm.test('Successfully retrieved team repositories', function () {", "    pm.response.to.have.status(200);", "    pm.expect(response).to.be.an('array');", "});"], "type": "text/javascript"}}]}, {"name": "List Team Members", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orgs/{{org}}/teams/{{team_slug}}/members", "host": ["{{baseUrl}}"], "path": ["orgs", "{{org}}", "teams", "{{team_slug}}", "members"]}}}]}, {"name": "Repository Search", "item": [{"name": "Search Repositories in Organization", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/search/repositories?q=org:{{org}}&per_page=100&sort=name", "host": ["{{baseUrl}}"], "path": ["search", "repositories"], "query": [{"key": "q", "value": "org:{{org}}"}, {"key": "per_page", "value": "100"}, {"key": "sort", "value": "name"}]}}, "event": [{"listen": "test", "script": {"exec": ["// Extract repository names from search results", "const response = pm.response.json();", "const repoNames = response.items.map(repo => repo.name);", "const csvList = repoNames.join(', ');", "", "console.log('All Organization Repositories (CSV):');", "console.log(csvList);", "", "pm.environment.set('all_org_repos_csv', csvList);", "", "console.log(`\\nTotal repositories found: ${response.total_count}`);"], "type": "text/javascript"}}]}]}]}