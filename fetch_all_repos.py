#!/usr/bin/env python3
"""
<PERSON>ript to fetch all repositories from GitHub API and save them to a CSV file.
This script handles pagination to get all repositories.
"""

import requests
import csv
import json
import os
from typing import List, Dict, Any

def fetch_all_repositories(org_name: str, token: str = None) -> List[Dict[str, Any]]:
    """
    Fetch all repositories for an organization using GitHub API with pagination.
    
    Args:
        org_name: GitHub organization name
        token: GitHub personal access token (optional but recommended for higher rate limits)
    
    Returns:
        List of repository dictionaries
    """
    
    base_url = f"https://api.github.com/orgs/{org_name}/repos"
    headers = {
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'Repository-Extractor'
    }
    
    # Add authorization header if token is provided
    if token:
        headers['Authorization'] = f'token {token}'
    
    all_repos = []
    page = 1
    per_page = 100  # Maximum allowed by GitHub API
    
    print(f"Fetching repositories for organization: {org_name}")
    
    while True:
        # Make API request
        params = {
            'page': page,
            'per_page': per_page,
            'type': 'all',  # Get all types of repositories
            'sort': 'name'  # Sort by name for consistent ordering
        }
        
        print(f"Fetching page {page}...")
        
        try:
            response = requests.get(base_url, headers=headers, params=params)
            response.raise_for_status()
            
            repos = response.json()
            
            # If no repositories returned, we've reached the end
            if not repos:
                break
                
            all_repos.extend(repos)
            print(f"  Found {len(repos)} repositories on page {page}")
            
            # If we got fewer than per_page results, this is the last page
            if len(repos) < per_page:
                break
                
            page += 1
            
        except requests.exceptions.RequestException as e:
            print(f"Error fetching repositories: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response status: {e.response.status_code}")
                print(f"Response body: {e.response.text}")
            break
    
    print(f"Total repositories fetched: {len(all_repos)}")
    return all_repos

def save_repos_to_csv(repos: List[Dict[str, Any]], filename: str = 'all_repositories.csv'):
    """
    Save repository data to CSV file.
    
    Args:
        repos: List of repository dictionaries
        filename: Output CSV filename
    """
    
    if not repos:
        print("No repositories to save.")
        return
    
    # Define the fields we want to extract
    fields = [
        'name',
        'full_name',
        'description',
        'private',
        'language',
        'created_at',
        'updated_at',
        'pushed_at',
        'size',
        'stargazers_count',
        'watchers_count',
        'forks_count',
        'open_issues_count',
        'default_branch',
        'archived',
        'disabled',
        'html_url',
        'clone_url'
    ]
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fields)
        writer.writeheader()
        
        for repo in repos:
            # Extract only the fields we want, handling missing fields gracefully
            row = {}
            for field in fields:
                row[field] = repo.get(field, '')
            writer.writerow(row)
    
    print(f"Repository data saved to {filename}")

def main():
    """Main function to orchestrate the repository fetching and saving."""

    # Configuration
    org_name = "cox-cei"  # Change this to your organization name

    # GitHub token (required for private repositories)
    token = os.getenv('GITHUB_TOKEN')

    if not token:
        print("ERROR: GitHub token is required to access private repositories!")
        print()
        print("To get all repositories, you need to:")
        print("1. Create a GitHub Personal Access Token:")
        print("   - Go to https://github.com/settings/tokens")
        print("   - Click 'Generate new token (classic)'")
        print("   - Select scopes: 'repo' (for private repos) and 'read:org'")
        print("   - Copy the generated token")
        print()
        print("2. Set the token as an environment variable:")
        print("   export GITHUB_TOKEN=your_personal_access_token")
        print()
        print("3. Run this script again")
        print()
        print("Alternative: If you have the repos.json file with all repositories,")
        print("you can use the extract_repo_names.py script instead.")
        return

    print(f"Using GitHub token to fetch repositories for organization: {org_name}")

    # Fetch all repositories
    repos = fetch_all_repositories(org_name, token)

    if repos:
        # Save to CSV
        save_repos_to_csv(repos, 'all_repositories.csv')

        # Also save just the names to a simple CSV (like the original request)
        with open('all_repository_names.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Repository Name'])
            for repo in repos:
                writer.writerow([repo.get('name', '')])

        print("Repository names also saved to all_repository_names.csv")

        # Print summary
        print("\nSummary:")
        print(f"Total repositories: {len(repos)}")

        # Count by language
        languages = {}
        for repo in repos:
            lang = repo.get('language') or 'Unknown'
            languages[lang] = languages.get(lang, 0) + 1

        print("Top languages:")
        for lang, count in sorted(languages.items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"  {lang}: {count}")

    else:
        print("No repositories found or error occurred.")
        print("This might be because:")
        print("1. The GitHub token doesn't have the right permissions")
        print("2. The organization name is incorrect")
        print("3. Network connectivity issues")

if __name__ == "__main__":
    main()
