#!/usr/bin/env python3
"""
Script to extract repository names from repos.json and save them to a CSV file.
"""

import json
import csv
import os

def extract_repo_names():
    """Extract repository names from the JSON file and save to CSV."""
    
    # Input and output file paths
    json_file = '.github/workflows/repos.json'
    csv_file = 'repository_names.csv'
    
    try:
        # Read the JSON file
        with open(json_file, 'r', encoding='utf-8') as f:
            repos_data = json.load(f)
        
        # Extract repository names
        repo_names = []
        for repo in repos_data:
            if 'name' in repo:
                repo_names.append(repo['name'])
        
        # Write to CSV file
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # Write header
            writer.writerow(['Repository Name'])
            
            # Write repository names
            for name in repo_names:
                writer.writerow([name])
        
        print(f"Successfully extracted {len(repo_names)} repository names to {csv_file}")
        print(f"First few repositories:")
        for i, name in enumerate(repo_names[:5]):
            print(f"  {i+1}. {name}")
        
        if len(repo_names) > 5:
            print(f"  ... and {len(repo_names) - 5} more")
            
    except FileNotFoundError:
        print(f"Error: Could not find the file {json_file}")
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format in {json_file}: {e}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    extract_repo_names()
