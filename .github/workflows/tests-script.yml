name: TEST - SSH and sudo Check

on:
  workflow_dispatch:
    inputs:
      server_hostname:
        description: 'Server hostname or IP address'
        required: true
        default: '*************'
      username:
        description: 'SSH username'
        required: true
        default: 'devops_dba'

jobs:
  ssh-sudo-test:
    runs-on: [Self-Hosted, Linux, MTP]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: SSH and sudo Test (simple directory listing)
        env:
          SSH_PASSWORD: ${{ secrets.SSH_PASSWORD }}
        run: |
          echo "Testing connection and sudo permissions on ${{ inputs.server_hostname }}..."
          sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no \
            ${{ inputs.username }}@${{ inputs.server_hostname }} \
            'sudo -u applpchebs bash -c "ls -la /EBS_share/EBSPCH/SOX_AUDIT"'
