run-name: ${{ github.event.inputs.server_hostname }} - SOX Audit Report Generation
name: EBS SOX Audit Report Generation

on:
  workflow_dispatch:
    inputs:
      server_hostname:
        description: 'Server hostname'
        required: true
        default: 'ceiocidxa0024.devf4.com'
      sharepoint_folder:
        description: 'SharePoint folder path'
        required: true
        default: 'Shared Documents/Projects/Security_APR_2025/EBS_12.2.13_19c'

jobs:
  generate-and-upload-reports:
    runs-on: [Self-Hosted, Linux, MTP]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

   #   - name: Setup SSH key
   #     uses: webfactory/ssh-agent@v0.7.0
   #     with:
   #       SSH_PASSWORD: ${{ secrets.SSH_PASSWORD_ }}
   #       SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY_ }}

      - name: Execute SQL scripts on server
        env:
          SSH_PASSWORD: ${{ secrets.SSH_PASSWORD_ }}
          SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY_ }}
        run: |
          echo "$SSH_PRIVATE_KEY" > key.pem
          chmod 600 key.pem
          sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no -i key.pem devops_dba@${{ inputs.server_hostname }} << 'EOF'
          sudo -su applpchebs bash << 'INNER_EOF'
          set -e
          . /ebspch/EBSapps.env run
          cd /EBS_share/EBSPCH/SOX_AUDIT

          echo "Executing EBS_DB_ALL_USERS.sql..."
          sqlplus -s APPS_QUERY/Ebspch_b4DqeT_LXIV @EBS_DB_ALL_USERS.sql

          echo "Executing Super_User_Audit.sql..."
          sqlplus -s APPS_QUERY/Ebspch_b4DqeT_LXIV @Super_User_Audit.sql

          echo "Executing Privileged_Access_Temp.sql..."
          sqlplus -s APPS_QUERY/Ebspch_b4DqeT_LXIV @Privileged_Access_Temp.sql

          echo "SQL scripts execution completed"
          exit 0
          INNER_EOF
          exit 0
          EOF
       #   sqlplus -s apps_query/${{ secrets.SQL_PASSWORD }} @EBS_DB_ALL_USERS.sql
       #             sqlplus -s apps_query/${{ secrets.SQL_PASSWORD }} @Super_User_Audit.sql
       #                       sqlplus -s apps_query/${{ secrets.SQL_PASSWORD }} @Privileged_Access_Temp.sql

      - name: Create local directory for outputs
        run: mkdir -p ./sox_audit_reports

      - name: Download output files
        env:
          SSH_PASSWORD: ${{ secrets.SSH_PASSWORD_ }}
          SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY_ }}
        run: |
          echo "$SSH_PRIVATE_KEY" > key.pem
          chmod 600 key.pem
          scp -o StrictHostKeyChecking=no -i key.pem devops_dba@${{ inputs.server_hostname }}:/EBS_share/EBSPCH/SOX_AUDIT/*.csv ./sox_audit_reports/
          
      - name: Upload to GitHub as artifacts
        uses: actions/upload-artifact@v4
        with:
          name: sox-audit-reports
          path: ./sox_audit_reports/
          retention-days: 30
          
      - name: Get Microsoft Graph access token
        id: get-token
        run: |
          RESPONSE=$(curl -s -X POST \
            -d "grant_type=client_credentials" \
            -d "client_id=${{ secrets.SHAREPOINT_CLIENT_ID }}" \
            -d "client_secret=${{ secrets.SHAREPOINT_CLIENT_SECRET }}" \
            -d "scope=https://graph.microsoft.com/.default" \
            "https://login.microsoftonline.com/${{ secrets.SHAREPOINT_TENANT_ID }}/oauth2/v2.0/token")
          TOKEN=$(echo $RESPONSE | jq -r '.access_token')
          echo "token=$TOKEN" >> $GITHUB_OUTPUT

      - name: Get SharePoint site and drive info
        id: site-info
        run: |
          SITE_RESPONSE=$(curl -s -H "Authorization: Bearer ${{ steps.get-token.outputs.token }}" \
            "https://graph.microsoft.com/v1.0/sites/coxinc.sharepoint.com:/sites/SharedServicesOpsAMS")
          SITE_ID=$(echo $SITE_RESPONSE | jq -r '.id')
          
          DRIVE_RESPONSE=$(curl -s -H "Authorization: Bearer ${{ steps.get-token.outputs.token }}" \
            "https://graph.microsoft.com/v1.0/sites/$SITE_ID/drives")
          DRIVE_ID=$(echo $DRIVE_RESPONSE | jq -r '.value[0].id')
          
          echo "site_id=$SITE_ID" >> $GITHUB_OUTPUT
          echo "drive_id=$DRIVE_ID" >> $GITHUB_OUTPUT

      - name: Upload files to SharePoint via Graph API
        run: |
          FOLDER_PATH="${{ inputs.sharepoint_folder }}"
          
          for FILE in ./sox_audit_reports/*.csv; do
            if [ -f "$FILE" ]; then
              FILE_NAME=$(basename "$FILE")
              echo "Uploading $FILE_NAME via Graph API..."
              
              RESPONSE=$(curl -s -X PUT \
                -H "Authorization: Bearer ${{ steps.get-token.outputs.token }}" \
                -H "Content-Type: application/octet-stream" \
                --data-binary @"$FILE" \
                "https://graph.microsoft.com/v1.0/sites/${{ steps.site-info.outputs.site_id }}/drives/${{ steps.site-info.outputs.drive_id }}/root:/$FOLDER_PATH/$FILE_NAME:/content")
              
              if echo "$RESPONSE" | jq -e '.error' > /dev/null; then
                echo "Error uploading $FILE_NAME: $RESPONSE"
                exit 1
              else
                echo "File $FILE_NAME uploaded successfully!"
              fi
            fi
          done
