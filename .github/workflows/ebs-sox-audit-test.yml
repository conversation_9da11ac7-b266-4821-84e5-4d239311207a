run-name: TEST - ${{ github.event.inputs.server_hostname }} - SOX Audit Report
name: TEST - EBS SOX Audit Report Generation

on:
  workflow_dispatch:
    inputs:
      server_hostname:
        description: 'Server hostname or IP address'
        required: true
        default: '*************'
      username:
        description: 'SSH username'
        required: true
        default: 'devops_dba'

jobs:
  test-server-connection:
    runs-on: [Self-Hosted, Linux, MTP]
    #runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      # - name: Install sshpass
      #   run: |
      #     echo "Installing sshpass..."
      #     apt-get update
      #     DEBIAN_FRONTEND=noninteractive apt-get install -y sshpass
      #   shell: sudo -n bash {0}
               
      - name: Test SSH connection and list directory
        env:
          SSH_PASSWORD: ${{ secrets.SSH_PASSWORD_ }}
          SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY_ }}
        run: |
          echo "$SSH_PRIVATE_KEY" > key.pem
          chmod 600 key.pem
          echo "Testing connection to ${{ inputs.server_hostname }}..."
          sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no -i key.pem ${{ inputs.username }}@${{ inputs.server_hostname }} "echo 'Connection successful!' && hostname && whoami"
          
         
          echo "Listing SOX_AUDIT directory..."
          sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no -i key.pem ${{ inputs.username }}@${{ inputs.server_hostname }}  << 'EOF'
            sudo -su applpchebs
            whoami
            ls -la /EBS_share/EBSPCH/SOX_AUDIT/
          EOF
          
      - name: Create local directory for outputs
        run: mkdir -p ./sox_audit_reports

      - name: Download a sample file (if exists)
        env:
          SSH_PASSWORD: ${{ secrets.SSH_PASSWORD_ }}
          SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY_ }}
        run: |
          echo "$SSH_PRIVATE_KEY" > key.pem
          chmod 600 key.pem
          # Try to download any existing CSV files for testing
          sshpass -p "$SSH_PASSWORD" scp -o StrictHostKeyChecking=no -i key.pem ${{ inputs.username }}@${{ inputs.server_hostname }}:/EBS_share/EBSPCH/SOX_AUDIT/*.csv ./sox_audit_reports/ || echo "No CSV files found"
          
          # If no CSV files exist, create a test file on the server and download it
          if [ ! "$(ls -A ./sox_audit_reports 2>/dev/null)" ]; then
            echo "No CSV files found. Creating a test file..."
            sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no -i key.pem ${{ inputs.username }}@${{ inputs.server_hostname }} "sudo -S <<< \"$SSH_PASSWORD\" su - applpchebs -c 'cd /EBS_share/EBSPCH/SOX_AUDIT && echo \"This is a test file\" > test_connection.csv'"
            
            sshpass -p "$SSH_PASSWORD" scp -o StrictHostKeyChecking=no -i key.pem ${{ inputs.username }}@${{ inputs.server_hostname }}:/EBS_share/EBSPCH/SOX_AUDIT/test_connection.csv ./sox_audit_reports/
          fi
          
          # List the downloaded files
          echo "Files downloaded:"
          ls -la ./sox_audit_reports/
          
      - name: Upload to GitHub as artifacts
        uses: actions/upload-artifact@v4
        with:
          name: sox-audit-test-files
          path: ./sox_audit_reports/
          retention-days: 1
